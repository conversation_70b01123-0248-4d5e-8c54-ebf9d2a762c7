#!/usr/bin/env python3
"""
Test script to verify REBA Video Analysis API deployment
"""

import requests
import json
import time
import sys
import os
from pathlib import Path

# Configuration
BASE_URL = "http://localhost:9000/api/v1"
TEST_VIDEO_PATH = None  # Will be set automatically if found

def find_test_video():
    """Find a test video in the uploads directory"""
    uploads_dir = Path("uploads")
    if uploads_dir.exists():
        for video_file in uploads_dir.rglob("*.mp4"):
            return str(video_file)
    return None

def test_health_check():
    """Test the health check endpoint"""
    print("🏥 Testing health check...")
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Health check passed")
            print(f"   Status: {health_data.get('status')}")
            print(f"   Database: {health_data.get('dependencies', {}).get('database')}")
            print(f"   Redis: {health_data.get('dependencies', {}).get('redis')}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_api_docs():
    """Test if API documentation is accessible"""
    print("\n📚 Testing API documentation...")
    try:
        response = requests.get(f"{BASE_URL}/docs", timeout=10)
        if response.status_code == 200:
            print("✅ API documentation accessible")
            return True
        else:
            print(f"❌ API docs failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API docs error: {e}")
        return False

def test_video_upload():
    """Test video upload functionality"""
    print("\n🎥 Testing video upload...")
    
    test_video = find_test_video()
    if not test_video:
        print("⚠️  No test video found in uploads directory")
        print("   Skipping video upload test")
        return True
    
    print(f"   Using test video: {test_video}")
    
    try:
        with open(test_video, 'rb') as f:
            files = {'file': (os.path.basename(test_video), f, 'video/mp4')}
            data = {
                'analysis_type': 'reba',
                'options': json.dumps({
                    "detection_confidence": 0.87,
                    "model_complexity": 2,
                    "max_duration_seconds": 10  # Short test
                }),
                'metadata': json.dumps({"test": "deployment_verification"})
            }
            
            response = requests.post(f"{BASE_URL}/videos/upload", files=files, data=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                job_id = result['id']
                print(f"✅ Video uploaded successfully")
                print(f"   Job ID: {job_id}")
                print(f"   Status: {result['status']}")
                return job_id
            else:
                print(f"❌ Video upload failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return None
                
    except Exception as e:
        print(f"❌ Video upload error: {e}")
        return None

def test_job_status(job_id):
    """Test job status checking"""
    if not job_id:
        return False
        
    print(f"\n📊 Testing job status for {job_id}...")
    
    try:
        response = requests.get(f"{BASE_URL}/jobs/{job_id}/status", timeout=10)
        if response.status_code == 200:
            status_data = response.json()
            print(f"✅ Job status retrieved")
            print(f"   Status: {status_data.get('status')}")
            print(f"   Progress: {status_data.get('progress_percentage', 0)}%")
            return True
        else:
            print(f"❌ Job status failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Job status error: {e}")
        return False

def test_system_stats():
    """Test system statistics endpoint"""
    print("\n📈 Testing system statistics...")
    try:
        response = requests.get(f"{BASE_URL}/health/stats", timeout=10)
        if response.status_code == 200:
            stats_data = response.json()
            print(f"✅ System stats retrieved")
            cpu_usage = stats_data.get('system', {}).get('cpu', {}).get('usage_percent', 0)
            memory_usage = stats_data.get('system', {}).get('memory', {}).get('usage_percent', 0)
            print(f"   CPU Usage: {cpu_usage:.1f}%")
            print(f"   Memory Usage: {memory_usage:.1f}%")
            return True
        else:
            print(f"❌ System stats failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ System stats error: {e}")
        return False

def main():
    """Run all deployment tests"""
    print("🚀 REBA Video Analysis API - Deployment Test")
    print("=" * 50)
    
    tests_passed = 0
    total_tests = 5
    
    # Test 1: Health Check
    if test_health_check():
        tests_passed += 1
    
    # Test 2: API Documentation
    if test_api_docs():
        tests_passed += 1
    
    # Test 3: Video Upload
    job_id = test_video_upload()
    if job_id:
        tests_passed += 1
    
    # Test 4: Job Status
    if test_job_status(job_id):
        tests_passed += 1
    
    # Test 5: System Stats
    if test_system_stats():
        tests_passed += 1
    
    # Summary
    print("\n" + "=" * 50)
    print(f"📋 Test Summary: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("🎉 All tests passed! Deployment is working correctly.")
        print("\n🔗 Access your API at:")
        print(f"   API Docs: {BASE_URL}/docs")
        print(f"   Health Check: {BASE_URL}/health")
        sys.exit(0)
    else:
        print("❌ Some tests failed. Please check the deployment.")
        sys.exit(1)

if __name__ == "__main__":
    main()
