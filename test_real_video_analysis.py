#!/usr/bin/env python3
"""
Test script to trigger real video analysis using an existing uploaded video.
"""

import uuid
import logging
from celery import Celery

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create Celery app instance
celery_app = Celery('test_client')
celery_app.config_from_object({
    'broker_url': 'redis://localhost:6379/0',
    'result_backend': 'redis://localhost:6379/0',
    'task_serializer': 'json',
    'accept_content': ['json'],
    'result_serializer': 'json',
    'timezone': 'UTC',
    'enable_utc': True,
})

def test_real_video_analysis():
    """Test real video analysis using an existing job."""
    logger.info("Testing real video analysis...")
    
    # Use an existing job ID that has a real video file
    existing_job_id = "147b4ef5-d944-489e-a057-697ea3d9ac3c"
    logger.info(f"Using existing job ID: {existing_job_id}")
    
    try:
        # Send the task
        logger.info("Dispatching real video analysis task to Celery...")
        result = celery_app.send_task(
            'app.services.video.tasks.process_video',
            args=[existing_job_id],
            queue='celery'
        )
        
        logger.info(f"Task dispatched successfully. Task ID: {result.id}")
        
        # Wait for the task to complete
        logger.info("Waiting for task to complete...")
        timeout = 60  # 60 seconds timeout for real video processing
        
        import time
        start_time = time.time()
        
        while not result.ready() and (time.time() - start_time) < timeout:
            logger.info(f"Task status: {result.status}")
            time.sleep(3)
        
        if result.ready():
            logger.info(f"Task completed! Status: {result.status}")
            if result.successful():
                logger.info(f"Task result: {result.result}")
                logger.info("✅ Real video analysis completed successfully!")
            else:
                logger.error(f"Task failed: {result.traceback}")
        else:
            logger.warning("Task did not complete within timeout")
            
        return result
        
    except Exception as e:
        logger.error(f"Error dispatching task: {e}")
        return None

def main():
    """Main test function."""
    logger.info("🚀 Starting Real Video Analysis Test")
    logger.info("=" * 50)
    
    # Test real video analysis
    result = test_real_video_analysis()
    if result and result.successful():
        logger.info("✅ Real video analysis test completed successfully")
    else:
        logger.error("❌ Real video analysis test failed")
    
    logger.info("=" * 50)
    logger.info("🏁 Real video analysis test completed")

if __name__ == "__main__":
    main()
