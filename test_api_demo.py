#!/usr/bin/env python3
"""
Demo script to test the REBA API functionality.
This script demonstrates how to use the API endpoints.
"""

import requests
import json
import time
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# API base URL
BASE_URL = "http://localhost:8000/api/v1"

def test_health_endpoint():
    """Test the health endpoint."""
    try:
        response = requests.get(f"{BASE_URL}/health")
        if response.status_code == 200:
            logger.info("✓ Health endpoint is working")
            return True
        else:
            logger.error(f"✗ Health endpoint failed: {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        logger.error("✗ Cannot connect to API. Make sure the server is running.")
        return False

def test_mock_video_upload():
    """Test video upload with mock processing."""
    logger.info("Testing mock video upload...")
    
    # Create a small dummy file for testing
    dummy_content = b"dummy video content for testing"
    
    files = {
        'file': ('test_video.mp4', dummy_content, 'video/mp4')
    }
    
    data = {
        'analysis_type': 'reba',
        'options': json.dumps({
            'use_mock_data': True,
            'generate_annotated_video': False,
            'frame_rate_reduction': 5
        }),
        'metadata': json.dumps({
            'description': 'Test video for REBA analysis',
            'source': 'API demo'
        })
    }
    
    try:
        response = requests.post(f"{BASE_URL}/videos/upload", files=files, data=data)
        
        if response.status_code == 200:
            result = response.json()
            job_id = result['id']
            logger.info(f"✓ Video uploaded successfully. Job ID: {job_id}")
            return job_id
        else:
            logger.error(f"✗ Video upload failed: {response.status_code}")
            logger.error(f"Response: {response.text}")
            return None
            
    except Exception as e:
        logger.error(f"✗ Error uploading video: {e}")
        return None

def test_job_status(job_id):
    """Test job status endpoint."""
    logger.info(f"Checking job status for {job_id}...")
    
    try:
        response = requests.get(f"{BASE_URL}/jobs/{job_id}/status")
        
        if response.status_code == 200:
            result = response.json()
            status = result['status']
            progress = result.get('progress_percentage', 0)
            logger.info(f"✓ Job status: {status} ({progress}% complete)")
            return status
        else:
            logger.error(f"✗ Failed to get job status: {response.status_code}")
            return None
            
    except Exception as e:
        logger.error(f"✗ Error getting job status: {e}")
        return None

def test_job_results(job_id):
    """Test job results endpoint."""
    logger.info(f"Getting job results for {job_id}...")
    
    try:
        response = requests.get(f"{BASE_URL}/jobs/{job_id}/results")
        
        if response.status_code == 200:
            result = response.json()
            logger.info("✓ Job results retrieved successfully")
            
            # Display key results
            if 'summary' in result:
                summary = result['summary']
                logger.info(f"  - REBA Score: {summary.get('reba_score', 'N/A')}")
                logger.info(f"  - Risk Level: {summary.get('risk_level', 'N/A')}")
                logger.info(f"  - Frames Analyzed: {summary.get('total_frames', 'N/A')}")
                logger.info(f"  - Detection Rate: {summary.get('detection_rate', 'N/A'):.1%}")
            
            return result
        else:
            logger.error(f"✗ Failed to get job results: {response.status_code}")
            logger.error(f"Response: {response.text}")
            return None
            
    except Exception as e:
        logger.error(f"✗ Error getting job results: {e}")
        return None

def test_analysis_summary(job_id):
    """Test analysis summary endpoint."""
    logger.info(f"Getting analysis summary for {job_id}...")
    
    try:
        response = requests.get(f"{BASE_URL}/analysis/{job_id}/summary")
        
        if response.status_code == 200:
            result = response.json()
            logger.info("✓ Analysis summary retrieved successfully")
            
            # Display key analysis data
            logger.info(f"  - Overall Risk Level: {result.get('overall_risk_level', 'N/A')}")
            logger.info(f"  - Final REBA Score: {result.get('final_reba_score', 'N/A')}")
            
            if 'analysis_stats' in result:
                stats = result['analysis_stats']
                logger.info(f"  - Total Duration: {stats.get('total_duration_seconds', 'N/A')} seconds")
                logger.info(f"  - Poses Detected: {stats.get('poses_detected', 'N/A')}")
            
            return result
        else:
            logger.error(f"✗ Failed to get analysis summary: {response.status_code}")
            return None
            
    except Exception as e:
        logger.error(f"✗ Error getting analysis summary: {e}")
        return None

def main():
    """Run the API demo."""
    logger.info("🚀 Starting REBA API Demo")
    logger.info("=" * 50)
    
    # Test 1: Health check
    if not test_health_endpoint():
        logger.error("❌ API is not available. Please start the server with:")
        logger.error("   uvicorn app.main:app --reload")
        return
    
    # Test 2: Upload video
    job_id = test_mock_video_upload()
    if not job_id:
        logger.error("❌ Video upload failed")
        return
    
    # Test 3: Check job status
    status = test_job_status(job_id)
    if not status:
        logger.error("❌ Job status check failed")
        return
    
    # Wait for processing to complete (with mock data, it should be instant)
    max_wait = 30  # seconds
    wait_time = 0
    while status in ['queued', 'processing'] and wait_time < max_wait:
        time.sleep(2)
        wait_time += 2
        status = test_job_status(job_id)
        if not status:
            break
    
    if status == 'completed':
        logger.info("✓ Job completed successfully")
        
        # Test 4: Get job results
        results = test_job_results(job_id)
        if results:
            logger.info("✓ Job results retrieved")
        
        # Test 5: Get analysis summary
        summary = test_analysis_summary(job_id)
        if summary:
            logger.info("✓ Analysis summary retrieved")
        
        logger.info("=" * 50)
        logger.info("🎉 All API tests completed successfully!")
        logger.info("The REBA analysis system is working correctly.")
        
    elif status == 'failed':
        logger.error("❌ Job processing failed")
    else:
        logger.error(f"❌ Job did not complete in time. Final status: {status}")

if __name__ == "__main__":
    main()
