from pydantic import BaseModel, UUID4, Field, validator
from typing import Optional, Dict, Any, List
from datetime import datetime
import uuid


class JobBase(BaseModel):
    analysis_type: str = "reba"
    processing_options: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None


class JobCreate(JobBase):
    pass


class FileInfo(BaseModel):
    filename: str
    size_bytes: int
    duration_seconds: Optional[float] = None
    fps: Optional[float] = None


class JobResponse(JobBase):
    id: UUID4
    status: str
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    file_info: Optional[FileInfo] = None
    error_message: Optional[str] = None
    
    class Config:
        from_attributes = True

    @classmethod
    def from_orm_with_file_info(cls, job):
        # Create a dict with job attributes
        job_dict = {
            "id": job.id,
            "status": job.status,
            "analysis_type": job.analysis_type,
            "created_at": job.created_at,
            "started_at": job.started_at,
            "completed_at": job.completed_at,
            "processing_options": job.processing_options,
            "metadata": job.job_metadata,  # Changed from job.metadata to job.job_metadata to match the database model
            "error_message": job.error_message,
        }
        
        # Add file_info if available
        if job.original_filename and job.file_size_bytes:
            job_dict["file_info"] = {
                "filename": job.original_filename,
                "size_bytes": job.file_size_bytes,
                "duration_seconds": job.duration_seconds,
                "fps": job.fps
            }
        
        return cls(**job_dict)


class JobStatusResponse(BaseModel):
    job_id: UUID4
    status: str
    progress_percentage: Optional[float] = 0
    current_stage: Optional[str] = None
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    processing_stats: Optional[Dict[str, Any]] = None
    
    class Config:
        from_attributes = True


class JobList(BaseModel):
    jobs: List[JobResponse]
    total: int
    page: int
    page_size: int


class ProcessingOptions(BaseModel):
    detection_confidence: float = 0.87
    model_complexity: int = 2
    frame_rate_reduction: int = 1
    output_format: List[str] = ["json"]


class JobMetadata(BaseModel):
    worker_id: Optional[str] = None
    task_description: Optional[str] = None
    department: Optional[str] = None
