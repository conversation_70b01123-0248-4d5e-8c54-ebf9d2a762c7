#!/bin/bash

# Test the full video upload and processing workflow using curl

BASE_URL="http://localhost:9000/api/v1"

echo "=== Testing Full Video Upload and Processing Workflow ==="

# Find an existing video file to test with
TEST_VIDEO=""
for video_path in ./uploads/*/*.mp4; do
    if [ -f "$video_path" ]; then
        TEST_VIDEO="$video_path"
        break
    fi
done

if [ -z "$TEST_VIDEO" ]; then
    echo "❌ No test video file found in uploads directory"
    exit 1
fi

echo "📹 Using test video: $TEST_VIDEO"

# Step 1: Upload video
echo ""
echo "Step 1: Uploading video..."

UPLOAD_RESPONSE=$(curl -s -X POST \
    -F "file=@$TEST_VIDEO" \
    -F "analysis_type=reba" \
    -F "options={\"detection_confidence\": 0.87, \"model_complexity\": 0}" \
    -F "metadata={\"test\": \"full_workflow\"}" \
    "$BASE_URL/videos/upload")

echo "Upload response: $UPLOAD_RESPONSE"

# Extract job ID from response (simple grep approach)
JOB_ID=$(echo "$UPLOAD_RESPONSE" | grep -o '"id":"[^"]*"' | cut -d'"' -f4)

if [ -z "$JOB_ID" ]; then
    echo "❌ Failed to extract job ID from upload response"
    exit 1
fi

echo "✅ Video uploaded successfully. Job ID: $JOB_ID"

# Step 2: Monitor job status
echo ""
echo "Step 2: Monitoring job status..."

MAX_WAIT=60  # 60 seconds
CHECK_INTERVAL=5  # 5 seconds
ELAPSED=0

while [ $ELAPSED -lt $MAX_WAIT ]; do
    echo "⏱️  Checking job status (${ELAPSED}s elapsed)..."

    STATUS_RESPONSE=$(curl -s "$BASE_URL/jobs/$JOB_ID")
    echo "Status response: $STATUS_RESPONSE"

    # Extract status from response
    STATUS=$(echo "$STATUS_RESPONSE" | grep -o '"status":"[^"]*"' | cut -d'"' -f4)

    echo "📊 Job status: $STATUS"

    case "$STATUS" in
        "completed")
            echo "✅ Job completed successfully!"

            # Try to extract REBA score
            REBA_SCORE=$(echo "$STATUS_RESPONSE" | grep -o '"reba_score":[^,}]*' | cut -d':' -f2)
            if [ -n "$REBA_SCORE" ]; then
                echo "🎯 REBA Score: $REBA_SCORE"
            fi

            # Try to extract risk level
            RISK_LEVEL=$(echo "$STATUS_RESPONSE" | grep -o '"overall_risk_level":"[^"]*"' | cut -d'"' -f4)
            if [ -n "$RISK_LEVEL" ]; then
                echo "⚠️  Risk Level: $RISK_LEVEL"
            fi

            echo ""
            echo "🎉 Full workflow test completed successfully!"
            exit 0
            ;;
        "failed")
            echo "❌ Job failed!"
            ERROR_MSG=$(echo "$STATUS_RESPONSE" | grep -o '"error_message":"[^"]*"' | cut -d'"' -f4)
            if [ -n "$ERROR_MSG" ]; then
                echo "💥 Error: $ERROR_MSG"
            fi
            exit 1
            ;;
        "queued"|"processing")
            echo "⏳ Job is $STATUS, waiting..."
            ;;
        *)
            echo "❓ Unknown status: $STATUS"
            ;;
    esac

    sleep $CHECK_INTERVAL
    ELAPSED=$((ELAPSED + CHECK_INTERVAL))
done

echo "⏰ Job monitoring timed out after ${MAX_WAIT} seconds"
echo "🔍 Final status check..."
FINAL_RESPONSE=$(curl -s "$BASE_URL/jobs/$JOB_ID")
echo "Final response: $FINAL_RESPONSE"
