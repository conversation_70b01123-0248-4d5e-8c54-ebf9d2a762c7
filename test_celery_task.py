#!/usr/bin/env python3
"""
Test script to manually trigger video processing task
"""
import sys
import os
import time
import logging

# Add the app directory to Python path
sys.path.append('/app')

from app.core.celery_app import celery_app

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_video_processing():
    """Test video processing with an existing video"""

    # Use the most recent job from the upload test
    test_job_id = "139a7d2d-0781-4735-a1fe-1ad13aac8327"  # The most recent job from upload test

    logger.info(f"Testing video processing for job ID: {test_job_id}")

    try:
        # Send the task to Celery
        logger.info("Dispatching video processing task...")
        result = celery_app.send_task(
            'app.services.video.tasks.process_video',
            args=[test_job_id],
            queue='celery'  # Use the default queue
        )

        logger.info(f"Task dispatched successfully. Task ID: {result.id}")

        # Wait for the task to complete
        logger.info("Waiting for task to complete...")
        timeout = 120  # 2 minutes timeout

        start_time = time.time()

        while not result.ready() and (time.time() - start_time) < timeout:
            logger.info(f"Task status: {result.status}")
            time.sleep(5)

        if result.ready():
            logger.info(f"Task completed! Status: {result.status}")
            if result.successful():
                logger.info(f"Task result: {result.result}")
            else:
                logger.error(f"Task failed: {result.result}")
        else:
            logger.warning("Task timed out")

    except Exception as e:
        logger.error(f"Error testing video processing: {e}")

if __name__ == "__main__":
    test_video_processing()
