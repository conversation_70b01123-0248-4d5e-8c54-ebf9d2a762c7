import cv2
import mediapipe as mp
import numpy as np
import logging
import time
import uuid
import math
import os
from typing import Dict, Any

from app.services.analysis.reba_calculator import (
    calculate_reba_components,
    calculate_final_reba_score,
    consolidate_pose_events,
    calculate_angle_statistics,
    calculate_overall_reba_scores
)
from app.services.analysis.db_utils import save_results_to_db

logger = logging.getLogger(__name__)

# Initialize MediaPipe Pose
mp_pose = mp.solutions.pose
mp_drawing = mp.solutions.drawing_utils
pose_connections = mp_pose.POSE_CONNECTIONS
landmarks_enum = mp_pose.PoseLandmark

# -------------------- Helper Functions --------------------

def angle_between(p1, p2, p3):
    """
    Calculate the angle between three points.
    """
    a = math.dist(p1, p2)
    b = math.dist(p3, p2)
    c = math.dist(p1, p3)
    try:
        angle = math.acos((a**2 + b**2 - c**2) / (2 * a * b))
        return math.degrees(angle)
    except:
        return 0

def get_neck_flexion_angle(ear_avg, shoulders_center):
    """
    Calculate neck flexion angle.
    """
    dx = ear_avg[0] - shoulders_center[0]
    dy = shoulders_center[1] - ear_avg[1]
    angle_rad = math.atan2(dx, dy)
    return (math.degrees(angle_rad))

def get_trunk_angle_yaxis(shoulder, hip):
    """
    Calculate trunk angle relative to y-axis.
    """
    dx = hip[0] - shoulder[0]
    dy = shoulder[1] - hip[1]
    try:
        angle = math.atan2(abs(dx), abs(dy))
        return (math.degrees(angle))
    except:
        return 0

def get_shoulder_angle_yaxis(elbow, shoulder):
    """
    Calculate shoulder angle relative to y-axis.
    """
    dx = elbow[0] - shoulder[0]
    dy = shoulder[1] - elbow[1]
    try:
        angle = math.atan2(abs(dx), abs(dy))
        return abs(math.degrees(angle))
    except:
        return 0

def get_neck_twist_lateral(left_ear, right_ear):
    """
    Calculate neck twist based on lateral ear positions.
    """
    dx = left_ear[0] - right_ear[0]
    return abs(dx)

def get_trunk_twist_lateral(left_shoulder, right_shoulder):
    """
    Calculate trunk twist based on lateral shoulder positions.
    """
    dx = left_shoulder[0] - right_shoulder[0]
    return abs(dx)

# -------------------- REBA Scoring Functions (from sample.py) --------------------

def reba_score_neck(angle):
    """Calculate REBA score for neck angle."""
    if angle < 20:
        return "Neck 0 points", 0
    elif angle < 30:
        return "Neck 1 point", 1
    else:
        return "Neck 2 points", 2

def reba_score_arm(angle):
    """Calculate REBA score for arm angle."""
    if 60 <= angle <= 100:
        return "Arm 1 point", 1
    else:
        return "Arm 2 points", 2

def reba_score_neck_twist(value):
    """Calculate REBA score for neck twist."""
    if value <= 5:
        return "Neck twist modifier 0 point", 0
    else:
        return "Neck twist modifier 1 point", 1

def reba_score_trunk_twist(value):
    """Calculate REBA score for trunk twist."""
    if value <= 7:
        return "Trunk twist modifier 0 point", 0
    else:
        return "Trunk twist modifier 1 point", 1

def reba_score_leg(knee_angle):
    """Calculate REBA score for leg angle."""
    if knee_angle > 150:
        return "Leg 1 point", 1
    elif 120 <= knee_angle <= 150:
        return "Leg 2 points", 2
    else:
        return "Leg 3 points", 3

def reba_score_trunk(angle):
    """Calculate REBA score for trunk angle."""
    if angle < 5:
        return "Trunk 1 point", 1
    elif angle < 20:
        return "Trunk 2 points", 2
    elif angle < 60:
        return "Trunk 3 points", 3
    else:
        return "Trunk 4 points", 4

def reba_score_shoulder(angle):
    """Calculate REBA score for shoulder angle."""
    if angle < 20:
        return "Shoulder 1 point", 1
    elif angle < 45:
        return "Shoulder 2 points", 2
    elif angle < 90:
        return "Shoulder 3 points", 3
    else:
        return "Shoulder 4 points", 4

def reba_score_wrist(angle):
    """Calculate REBA score for wrist angle."""
    if angle < 165:
        return "Wrist 2 points", 2
    else:
        return "Wrist 1 point", 1

def reba_score_legdiff(angle):
    """Calculate REBA score for leg difference."""
    if angle < 20:
        return "Leg difference 1 point", 1
    else:
        return "Leg difference 2 points", 2

def get_color_for_score(score):
    """
    Get color for visualization based on REBA score.
    """
    if score == 0:
        return (0, 255, 0)     # Green
    elif score == 1:
        return (0, 255, 255)   # Yellow
    elif score == 2:
        return (0, 165, 255)   # Orange
    elif score == 3:
        return (0, 0, 255)     # Red
    elif score == 4:
        return (0, 0, 0)       # Black
    return (255, 255, 255)     # Default white

def update_frame_counts(counter_dict, label):
    """Update frame count for a given label."""
    if label not in counter_dict:
        counter_dict[label] = 0
    counter_dict[label] += 1

def annotate_frame_with_reba_analysis(frame, landmarks, angles, width, height):
    """
    Annotate frame with REBA analysis results, similar to sample.py.
    """
    try:
        # Helper function to convert normalized coordinates to pixel coordinates
        def to_xy(landmark):
            return int(landmark.x * width), int(landmark.y * height)

        # Get key points
        l_shoulder = to_xy(landmarks[landmarks_enum.LEFT_SHOULDER])
        r_shoulder = to_xy(landmarks[landmarks_enum.RIGHT_SHOULDER])
        l_ear = to_xy(landmarks[landmarks_enum.LEFT_EAR])
        r_ear = to_xy(landmarks[landmarks_enum.RIGHT_EAR])
        l_elbow = to_xy(landmarks[landmarks_enum.LEFT_ELBOW])
        r_elbow = to_xy(landmarks[landmarks_enum.RIGHT_ELBOW])
        l_wrist = to_xy(landmarks[landmarks_enum.LEFT_WRIST])
        r_wrist = to_xy(landmarks[landmarks_enum.RIGHT_WRIST])
        l_hip = to_xy(landmarks[landmarks_enum.LEFT_HIP])
        r_hip = to_xy(landmarks[landmarks_enum.RIGHT_HIP])
        l_knee = to_xy(landmarks[landmarks_enum.LEFT_KNEE])
        r_knee = to_xy(landmarks[landmarks_enum.RIGHT_KNEE])
        l_ankle = to_xy(landmarks[landmarks_enum.LEFT_ANKLE])
        r_ankle = to_xy(landmarks[landmarks_enum.RIGHT_ANKLE])
        l_index = to_xy(landmarks[landmarks_enum.LEFT_INDEX])
        r_index = to_xy(landmarks[landmarks_enum.RIGHT_INDEX])

        # Calculate center points
        mid_shoulder = ((l_shoulder[0] + r_shoulder[0]) // 2, (l_shoulder[1] + r_shoulder[1]) // 2)
        mid_hip = ((l_hip[0] + r_hip[0]) // 2, (l_hip[1] + r_hip[1]) // 2)
        mid_ear = ((l_ear[0] + r_ear[0]) // 2, (l_ear[1] + r_ear[1]) // 2)
        mid_hip_vis = ((l_hip[0] + r_hip[0]) // 2 + 10, (l_hip[1] + r_hip[1]) // 2 - 10)
        mid_ear_vis = ((l_ear[0] + r_ear[0]) // 2 + 10, (l_ear[1] + r_ear[1]) // 2 - 10)

        # Calculate REBA scores for visualization
        neck_label, neck_score = reba_score_neck(abs(angles.get('neck', 0)))
        trunk_label, trunk_score = reba_score_trunk(abs(angles.get('trunk', 0)))

        # Get colors for visualization
        neck_color = get_color_for_score(neck_score)
        trunk_color = get_color_for_score(trunk_score)

        # Draw neck and trunk lines
        cv2.line(frame, mid_shoulder, mid_ear, neck_color, 2)
        cv2.line(frame, mid_shoulder, mid_hip, trunk_color, 2)

        # Process arms
        for side in ["LEFT", "RIGHT"]:
            if side == "LEFT":
                shoulder, elbow, wrist, index = l_shoulder, l_elbow, l_wrist, l_index
                arm_angle = angles.get('left_arm', 0)
                shoulder_angle = angles.get('left_shoulder', 0)
                wrist_angle = angles.get('left_wrist', 0)
            else:
                shoulder, elbow, wrist, index = r_shoulder, r_elbow, r_wrist, r_index
                arm_angle = angles.get('right_arm', 0)
                shoulder_angle = angles.get('right_shoulder', 0)
                wrist_angle = angles.get('right_wrist', 0)

            # Calculate REBA scores
            arm_label, arm_score = reba_score_arm(abs(arm_angle))
            shoulder_label, shoulder_score = reba_score_shoulder(abs(shoulder_angle))
            wrist_label, wrist_score = reba_score_wrist(abs(wrist_angle))

            # Get colors
            arm_color = get_color_for_score(arm_score)
            shoulder_color = get_color_for_score(shoulder_score)
            wrist_color = get_color_for_score(wrist_score)

            # Draw lines
            cv2.line(frame, shoulder, elbow, shoulder_color, 2)
            cv2.line(frame, elbow, wrist, arm_color, 2)
            cv2.line(frame, wrist, index, wrist_color, 2)

            # Add text annotations
            cv2.putText(frame, f"{side.title()} {arm_label}", (elbow[0] + 10, elbow[1] - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, arm_color, 2)
            cv2.putText(frame, f"{side.title()} {shoulder_label}", (shoulder[0] + 10, shoulder[1] - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, shoulder_color, 2)
            cv2.putText(frame, f"{side.title()} {wrist_label}", (wrist[0] + 10, wrist[1] - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, wrist_color, 2)

        # Process legs
        for side in ["LEFT", "RIGHT"]:
            if side == "LEFT":
                hip, knee, ankle = l_hip, l_knee, l_ankle
                knee_angle = angles.get('left_leg', 0)
            else:
                hip, knee, ankle = r_hip, r_knee, r_ankle
                knee_angle = angles.get('right_leg', 0)

            leg_label, leg_score = reba_score_leg(knee_angle)
            leg_color = get_color_for_score(leg_score)

            # Draw leg lines
            cv2.line(frame, hip, knee, leg_color, 2)
            cv2.line(frame, knee, ankle, leg_color, 2)

            # Add text annotation
            cv2.putText(frame, f"{side.title()} {leg_label}", (knee[0] + 10, knee[1] - 10),
                       cv2.FONT_HERSHEY_SIMPLEX, 0.6, leg_color, 2)

        # Draw remaining skeleton connections
        for connection in pose_connections:
            start_idx, end_idx = connection
            pt1 = to_xy(landmarks[start_idx])
            pt2 = to_xy(landmarks[end_idx])

            # Skip connections we've already drawn with colors
            if (start_idx, end_idx) in [
                (landmarks_enum.LEFT_SHOULDER, landmarks_enum.LEFT_ELBOW),
                (landmarks_enum.LEFT_ELBOW, landmarks_enum.LEFT_WRIST),
                (landmarks_enum.RIGHT_SHOULDER, landmarks_enum.RIGHT_ELBOW),
                (landmarks_enum.RIGHT_ELBOW, landmarks_enum.RIGHT_WRIST),
                (landmarks_enum.LEFT_HIP, landmarks_enum.LEFT_KNEE),
                (landmarks_enum.LEFT_KNEE, landmarks_enum.LEFT_ANKLE),
                (landmarks_enum.RIGHT_HIP, landmarks_enum.RIGHT_KNEE),
                (landmarks_enum.RIGHT_KNEE, landmarks_enum.RIGHT_ANKLE),
                (landmarks_enum.LEFT_WRIST, landmarks_enum.LEFT_INDEX),
                (landmarks_enum.RIGHT_WRIST, landmarks_enum.RIGHT_INDEX)
            ]:
                continue
            cv2.line(frame, pt1, pt2, (0, 255, 0), 2)

        # Add main text overlays
        cv2.putText(frame, neck_label, mid_ear_vis, cv2.FONT_HERSHEY_SIMPLEX, 0.6, neck_color, 2)
        cv2.putText(frame, trunk_label, mid_hip_vis, cv2.FONT_HERSHEY_SIMPLEX, 0.6, trunk_color, 2)

        return frame

    except Exception as e:
        logger.error(f"Error annotating frame: {e}")
        return frame

def calculate_angle(a, b, c) -> float:
    """
    Calculate the angle between three points using MediaPipe landmarks.
    """
    # Check visibility
    if a['visibility'] < 0.5 or b['visibility'] < 0.5 or c['visibility'] < 0.5:
        logger.debug(f"Low visibility for angle calculation: {a['visibility']}, {b['visibility']}, {c['visibility']}")
        return 0.0

    # Convert to numpy arrays
    a = np.array([a['x'], a['y'], a['z']])
    b = np.array([b['x'], b['y'], b['z']])
    c = np.array([c['x'], c['y'], c['z']])

    # Calculate vectors
    ba = a - b
    bc = c - b

    # Calculate angle
    cosine_angle = np.dot(ba, bc) / (np.linalg.norm(ba) * np.linalg.norm(bc))
    angle = np.arccos(np.clip(cosine_angle, -1.0, 1.0))

    # Convert to degrees
    angle = np.degrees(angle)

    return angle


def calculate_angles_from_landmarks(landmarks) -> Dict[str, float]:
    """
    Calculate joint angles from pose landmarks using the same logic as sample.py.
    """
    try:
        # Helper function to convert landmark to xy coordinates
        def to_xy(landmark, width=1, height=1):
            return [landmark.x * width, landmark.y * height]

        # Extract key points (using normalized coordinates)
        l_shoulder = to_xy(landmarks[landmarks_enum.LEFT_SHOULDER])
        r_shoulder = to_xy(landmarks[landmarks_enum.RIGHT_SHOULDER])
        l_ear = to_xy(landmarks[landmarks_enum.LEFT_EAR])
        r_ear = to_xy(landmarks[landmarks_enum.RIGHT_EAR])
        l_elbow = to_xy(landmarks[landmarks_enum.LEFT_ELBOW])
        r_elbow = to_xy(landmarks[landmarks_enum.RIGHT_ELBOW])
        l_wrist = to_xy(landmarks[landmarks_enum.LEFT_WRIST])
        r_wrist = to_xy(landmarks[landmarks_enum.RIGHT_WRIST])
        l_hip = to_xy(landmarks[landmarks_enum.LEFT_HIP])
        r_hip = to_xy(landmarks[landmarks_enum.RIGHT_HIP])
        l_knee = to_xy(landmarks[landmarks_enum.LEFT_KNEE])
        r_knee = to_xy(landmarks[landmarks_enum.RIGHT_KNEE])
        l_ankle = to_xy(landmarks[landmarks_enum.LEFT_ANKLE])
        r_ankle = to_xy(landmarks[landmarks_enum.RIGHT_ANKLE])
        l_index = to_xy(landmarks[landmarks_enum.LEFT_INDEX])
        r_index = to_xy(landmarks[landmarks_enum.RIGHT_INDEX])

        # Calculate center points
        mid_shoulder = [(l_shoulder[0] + r_shoulder[0]) / 2, (l_shoulder[1] + r_shoulder[1]) / 2]
        mid_hip = [(l_hip[0] + r_hip[0]) / 2, (l_hip[1] + r_hip[1]) / 2]
        mid_ear = [(l_ear[0] + r_ear[0]) / 2, (l_ear[1] + r_ear[1]) / 2]

        # Calculate angles using the same functions as sample.py
        neck_angle = get_neck_flexion_angle(mid_ear, mid_shoulder)
        neck_twist = get_neck_twist_lateral(l_ear, r_ear)

        trunk_angle = get_trunk_angle_yaxis(mid_shoulder, mid_hip)
        trunk_twist = get_trunk_twist_lateral(l_shoulder, r_shoulder)

        # Arms - calculate for both sides
        left_arm_angle = angle_between(l_shoulder, l_elbow, l_wrist)
        right_arm_angle = angle_between(r_shoulder, r_elbow, r_wrist)
        left_shoulder_angle = get_shoulder_angle_yaxis(l_elbow, l_shoulder)
        right_shoulder_angle = get_shoulder_angle_yaxis(r_elbow, r_shoulder)
        left_wrist_angle = angle_between(l_elbow, l_wrist, l_index)
        right_wrist_angle = angle_between(r_elbow, r_wrist, r_index)

        # Legs
        left_knee_angle = angle_between(l_hip, l_knee, l_ankle)
        right_knee_angle = angle_between(r_hip, r_knee, r_ankle)
        leg_angle_diff = abs(left_knee_angle - right_knee_angle)

        return {
            'neck': neck_angle,
            'trunk': trunk_angle,
            'left_arm': left_arm_angle,
            'right_arm': right_arm_angle,
            'left_shoulder': left_shoulder_angle,
            'right_shoulder': right_shoulder_angle,
            'left_wrist': left_wrist_angle,
            'right_wrist': right_wrist_angle,
            'left_leg': left_knee_angle,
            'right_leg': right_knee_angle,
            'leg_diff': leg_angle_diff,
            'neck_twist': neck_twist,
            'trunk_twist': trunk_twist
        }
    except Exception as e:
        logger.error(f"Error calculating angles: {e}")
        return {}

def process_video_for_reba(
    video_path: str,
    job_id: uuid.UUID,
    options: Dict[str, Any]
) -> Dict[str, Any]:
    """
    Process a video file for REBA analysis using MediaPipe.

    Args:
        video_path: Path to the video file
        job_id: UUID of the job
        options: Processing options

    Returns:
        Dict containing the analysis results
    """
    start_time = time.time()
    logger.info(f"Starting REBA analysis for video: {video_path}")

    # Extract options with defaults
    use_mock_data = options.get('use_mock_data', False)
    frame_rate_reduction = options.get('frame_rate_reduction', 5)
    model_complexity = options.get('model_complexity', 0)  # Use lightest model (0) to avoid download issues
    min_detection_confidence = options.get('min_detection_confidence', 0.5)  # Lower threshold
    min_tracking_confidence = options.get('min_tracking_confidence', 0.5)
    generate_annotated_video = options.get('generate_annotated_video', True)
    max_duration_seconds = options.get('max_duration_seconds', 30)  # Process first 30 seconds for testing

    logger.info(f"Processing options: frame_rate_reduction={frame_rate_reduction}, "
               f"model_complexity={model_complexity}, "
               f"min_detection_confidence={min_detection_confidence}, "
               f"min_tracking_confidence={min_tracking_confidence}, "
               f"use_mock_data={use_mock_data}, "
               f"generate_annotated_video={generate_annotated_video}")

    # Initialize variables
    frame_buffer = []
    processed_frames = 0
    poses_detected = 0

    # Setup output paths for annotated video
    output_dir = None
    annotated_video_path = None
    video_writer = None

    if generate_annotated_video and not use_mock_data:
        # Create output directory
        output_dir = os.path.join(os.path.dirname(video_path), f"{job_id}_output")
        os.makedirs(output_dir, exist_ok=True)
        annotated_video_path = os.path.join(output_dir, f"{job_id}_annotated.mp4")

    # Initialize MediaPipe Pose if not using mock data
    if not use_mock_data:
        try:
            # Initialize MediaPipe Pose with the working configuration
            pose = mp_pose.Pose(
                static_image_mode=False,
                model_complexity=model_complexity,
                enable_segmentation=False,
                min_detection_confidence=min_detection_confidence,
                min_tracking_confidence=min_tracking_confidence
            )

            logger.info(f"MediaPipe Pose initialized successfully with model complexity {model_complexity}")

        except Exception as e:
            logger.info(f"[DEBUG] MediaPipe initialization failed: {e}")
            # Fall back to mock data if MediaPipe initialization fails
            use_mock_data = True
            logger.warning("Falling back to mock data due to MediaPipe initialization failure")

    # Generate mock data if requested
    if use_mock_data:
        logger.info(f"Using mock data for REBA analysis")

        # Define parameters for mock data generation
        mock_video_length_seconds = 120
        mock_fps = 30
        total_frames = mock_video_length_seconds * mock_fps

        # Generate mock frames
        for frame_idx in range(0, total_frames, frame_rate_reduction):
            timestamp = frame_idx / mock_fps

            # Simulate pose detection (70% success rate)
            pose_detected = (frame_idx % 10) < 7
            if pose_detected:
                poses_detected += 1

                # Generate mock angles
                mock_angles = {
                    'neck': 45.0,
                    'trunk': 30.0,
                    'upper_arm_right': 60.0,
                    'upper_arm_left': 60.0,
                    'lower_arm_right': 90.0,
                    'lower_arm_left': 90.0,
                    'wrist_right': 15.0,
                    'wrist_left': 15.0,
                    'legs': 160.0,
                    'neck_twist': 3.0,
                    'trunk_twist': 5.0
                }

                # Calculate REBA components
                mock_reba_components = calculate_reba_components(mock_angles)
                mock_final_score = calculate_final_reba_score(mock_reba_components)

                frame_buffer.append({
                    'frame_number': frame_idx,
                    'timestamp_seconds': timestamp,
                    'angles': mock_angles,
                    'reba_components': mock_reba_components,
                    'final_reba_score': mock_final_score,
                    'pose_detected': True
                })
            else:
                frame_buffer.append({
                    'frame_number': frame_idx,
                    'timestamp_seconds': timestamp,
                    'angles': {},
                    'reba_components': {},
                    'final_reba_score': None,
                    'pose_detected': False
                })

        logger.info(f"[DEBUG] Generating mock data for {len(frame_buffer)} frames with {poses_detected} poses detected")
        processed_frames = len(frame_buffer)
    else:
        # Process actual video
        try:
            # Open video file
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                logger.error(f"Failed to open video file: {video_path}")
                raise ValueError(f"Could not open video file: {video_path}")

            # Get video properties
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
            height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
            video_length_seconds = frame_count / fps if fps > 0 else 0

            logger.info(f"Video properties: fps={fps}, frame_count={frame_count}, "
                       f"width={width}, height={height}, length={video_length_seconds:.2f} seconds")

            # Initialize video writer for annotated output
            if generate_annotated_video:
                fourcc = cv2.VideoWriter_fourcc(*'mp4v')
                video_writer = cv2.VideoWriter(annotated_video_path, fourcc, fps, (width, height))
                logger.info(f"Initialized video writer for annotated output: {annotated_video_path}")

            # Process frames
            frame_idx = 0
            while cap.isOpened():
                # Calculate timestamp first to check duration limit
                timestamp = frame_idx / fps

                # Stop processing if we've reached the max duration
                if timestamp >= max_duration_seconds:
                    logger.info(f"Reached max duration of {max_duration_seconds} seconds, stopping processing")
                    break

                # Only process every nth frame based on frame_rate_reduction
                if frame_idx % frame_rate_reduction != 0:
                    success = cap.grab()
                    if not success:
                        break
                    frame_idx += 1
                    continue

                # Read frame
                success, frame = cap.read()
                if not success:
                    break

                # Convert to RGB for MediaPipe
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

                # Process with MediaPipe
                results = pose.process(frame_rgb)

                # Check if pose was detected
                if results.pose_landmarks:
                    poses_detected += 1

                    # Calculate angles using the comprehensive function from sample.py
                    angles = calculate_angles_from_landmarks(results.pose_landmarks.landmark)

                    # Calculate REBA components
                    reba_components = calculate_reba_components(angles)
                    final_reba_score = calculate_final_reba_score(reba_components)

                    # Annotate frame if generating annotated video
                    if generate_annotated_video and video_writer:
                        annotated_frame = annotate_frame_with_reba_analysis(
                            frame.copy(), results.pose_landmarks.landmark, angles, width, height
                        )
                        video_writer.write(annotated_frame)

                    frame_buffer.append({
                        'frame_number': frame_idx,
                        'timestamp_seconds': timestamp,
                        'angles': angles,
                        'reba_components': reba_components,
                        'final_reba_score': final_reba_score,
                        'pose_detected': True
                    })
                else:
                    # Write original frame if no pose detected
                    if generate_annotated_video and video_writer:
                        video_writer.write(frame)

                    frame_buffer.append({
                        'frame_number': frame_idx,
                        'timestamp_seconds': timestamp,
                        'angles': {},
                        'reba_components': {},
                        'final_reba_score': None,
                        'pose_detected': False
                    })

                # Increment frame index
                frame_idx += 1
                processed_frames += 1

                # Log progress periodically
                if processed_frames % 100 == 0:
                    logger.info(f"Processed {processed_frames} frames with {poses_detected} poses detected")

            # Release video capture
            cap.release()

            # Release video writer
            if video_writer:
                video_writer.release()
                logger.info(f"Annotated video saved to: {annotated_video_path}")

            # Release MediaPipe resources
            pose.close()

        except Exception as e:
            logger.error(f"Error processing video: {e}")
            # If we have no frames processed, generate mock data as fallback
            if len(frame_buffer) == 0:
                logger.warning("No frames processed. Falling back to mock data.")
                return process_video_for_reba(video_path, job_id, {**options, 'use_mock_data': True})

    # Calculate detection rate
    detection_rate = poses_detected / processed_frames if processed_frames > 0 else 0
    logger.info(f"Processed {processed_frames} frames with {poses_detected} poses detected ({detection_rate:.2%} detection rate)")

    # Consolidate frame-level data into pose events
    pose_events = consolidate_pose_events(frame_buffer, job_id)

    # Calculate angle statistics
    angle_statistics = calculate_angle_statistics(frame_buffer, job_id)

    # Calculate overall REBA scores
    overall_reba_scores = calculate_overall_reba_scores(pose_events, job_id)

    # Prepare results
    results = {
        'job_id': str(job_id),
        'video_path': video_path,
        'processing_time_seconds': time.time() - start_time,
        'frames_processed': processed_frames,
        'poses_detected': poses_detected,
        'detection_rate': detection_rate,
        'pose_events': pose_events,
        'angle_statistics': angle_statistics,
        'overall_reba_scores': overall_reba_scores,
        'used_mock_data': use_mock_data,
        'annotated_video_path': annotated_video_path if generate_annotated_video and not use_mock_data else None,
        'output_directory': output_dir
    }

    # Save results to database
    try:
        logger.info(f"Saving results to database for job {job_id}")
        save_results_to_db(
            job_id=job_id,
            processed_frames=processed_frames,
            poses_detected=poses_detected,
            detection_rate=detection_rate,
            pose_events=pose_events,
            angle_stats=angle_statistics,
            reba_scores=overall_reba_scores
        )
        logger.info(f"Results saved successfully")
    except Exception as e:
        logger.error(f"Error saving results to database: {e}")

    return results






