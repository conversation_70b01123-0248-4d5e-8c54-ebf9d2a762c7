import logging
import time
import async<PERSON>
import uuid
from typing import Dict, Any, List
from datetime import datetime

from app.db.database import Async<PERSON>essionLocal
from app.crud import reba as reba_crud
from app.crud import job as job_crud

logger = logging.getLogger(__name__)


def save_results_to_db(job_id: uuid.UUID, processed_frames: int, poses_detected: int, detection_rate: float,
                      pose_events: List[Dict[str, Any]], angle_stats: List[Dict[str, Any]], reba_scores: Dict[str, Any]):
    """
    Save analysis results to the database.
    """
    start_time = time.time()
    use_mock_db = True  # Set to False in production

    if use_mock_db:
        # For testing purposes, just log the data instead of saving to DB
        logger.info(f"[DEBUG] MOCK DB SAVE: Would save results for job {job_id}")
        logger.info(f"[DEBUG] MOCK DB SAVE: Processed {processed_frames} frames with {poses_detected} poses detected")
        logger.info(f"[DEBUG] MOCK DB SAVE: Overall risk level: {reba_scores.get('risk_level', 'Unknown')}")
        logger.info(f"[DEBUG] MOCK DB SAVE: Generated {len(pose_events)} pose events")
        logger.info(f"[DEBUG] MOCK DB SAVE: Generated {len(angle_stats)} angle statistics")

        # Format the enhanced response for the API
        enhanced_response = {
            "job_id": str(job_id),
            "analysis_summary": {
                "overall_risk_level": reba_scores.get("risk_level", "medium"),
                "total_duration_seconds": processed_frames / 30 if processed_frames else 0,  # Assuming 30 fps
                "frames_analyzed": processed_frames,
                "poses_detected": poses_detected,
                "detection_rate": detection_rate * 100 if detection_rate else 0
            },
            "reba_analysis": {
                "final_reba_score": reba_scores.get("final_reba_score", 5),
                "risk_level": reba_scores.get("risk_level", "medium"),
                "risk_description": reba_scores.get("risk_description", "Medium risk, further investigate, change soon"),
                "step_by_step_analysis": reba_scores.get("step_by_step_analysis", {}),
                "risk_interpretation": reba_scores.get("risk_interpretation", {})
            },
            "risk_events": [
                {
                    "timestamp": event.get("start_time_seconds", 0),
                    "duration_seconds": event.get("duration_seconds", 0),
                    "body_parts_affected": [event.get("body_part", "unknown")],
                    "max_individual_score": event.get("final_score", 0),
                    "combined_risk_score": event.get("final_score", 0),
                    "event_description": f"High risk posture detected - {event.get('pose_category', 'unknown')} in {event.get('body_part', 'unknown')}"
                } for event in pose_events if event.get("final_score", 0) >= 4
            ],
            "summary_statistics": reba_scores.get("summary_statistics", {})
        }

        # Store the enhanced response in the job metadata
        # In a real implementation, this would be saved to the database
        logger.info(f"[DEBUG] MOCK DB SAVE: Enhanced response prepared for job {job_id}")

        # Update the job status directly (bypassing async for testing)
        try:
            # This is a synchronous approach for testing
            # In production, we would use the async functions properly
            update_job_status_sync(job_id, "completed",
                                  reba_score=reba_scores.get("final_reba_score"),
                                  enhanced_response=enhanced_response)
        except Exception as e:
            logger.error(f"[DEBUG] Error updating job status: {str(e)}")

        logger.info(f"Saved mock analysis results for job {job_id}")
    else:
        # In a real implementation, we would properly handle the async DB operations
        try:
            # Create a new event loop for this thread if needed
            try:
                loop = asyncio.get_event_loop()
            except RuntimeError:
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)

            # Run the async database operations
            loop.run_until_complete(save_results_to_db_async(
                job_id, processed_frames, poses_detected, detection_rate,
                pose_events, angle_stats, reba_scores
            ))
        except Exception as e:
            logger.error(f"Error saving results to database: {str(e)}")
            raise

    logger.info(f"Database save completed in {time.time() - start_time:.2f} seconds")


def update_job_status_sync(job_id: uuid.UUID, status: str, **kwargs):
    """
    Update job status synchronously for testing purposes.
    This function directly updates the database using raw SQL to bypass the async requirements.
    """
    import psycopg2
    from app.core.config import settings

    logger.info(f"[DEBUG] Updating job {job_id} status to '{status}'")

    # Add timestamps based on status
    if status == "processing" and "started_at" not in kwargs:
        kwargs["started_at"] = datetime.utcnow()
    elif status in ["completed", "failed"] and "completed_at" not in kwargs:
        kwargs["completed_at"] = datetime.utcnow()

    # Log the additional fields we would update
    for key, value in kwargs.items():
        logger.info(f"[DEBUG] Setting {key} = {value}")

    # Extract database connection info from the async URL
    # Format is typically: postgresql+asyncpg://user:password@host:port/dbname
    # We need to convert it to a standard PostgreSQL connection string for psycopg2
    async_db_url = settings.DATABASE_URL

    # Convert the async URL to a standard PostgreSQL connection string
    # Remove the +asyncpg part and extract the components
    try:
        # Simple conversion - replace the driver prefix
        if 'postgresql+asyncpg://' in async_db_url:
            db_url = async_db_url.replace('postgresql+asyncpg://', 'postgresql://')
            # Remove SSL parameter that psycopg2 doesn't understand
            if '?ssl=disable' in db_url:
                db_url = db_url.replace('?ssl=disable', '')
        else:
            db_url = async_db_url
    except Exception as e:
        logger.error(f"[DEBUG] Error converting database URL: {str(e)}")
        db_url = async_db_url  # Use the original URL as fallback

    try:
        # Connect to the database directly
        conn = psycopg2.connect(db_url)
        cursor = conn.cursor()

        # Build the update SQL
        update_fields = [f"status = '{status}'"]

        if "started_at" in kwargs:
            update_fields.append(f"started_at = '{kwargs['started_at']}'")
        if "completed_at" in kwargs:
            update_fields.append(f"completed_at = '{kwargs['completed_at']}'")

        # Handle JSON data for job_metadata
        if "enhanced_response" in kwargs:
            import json
            # Convert the enhanced response to a JSON string
            enhanced_response_json = json.dumps(kwargs["enhanced_response"])
            # Store the entire enhanced response in the job_metadata field
            update_fields.append(f"job_metadata = '{enhanced_response_json}'::jsonb")
        elif "reba_score" in kwargs:
            # Store just the REBA score in the job_metadata JSON field
            update_fields.append(f"job_metadata = jsonb_set(COALESCE(job_metadata, '{{}}'::jsonb), '{{reba_score}}', '{kwargs['reba_score']}'::jsonb)")

        if "error_message" in kwargs:
            update_fields.append(f"error_message = '{kwargs['error_message']}'")


        update_sql = f"UPDATE jobs SET {', '.join(update_fields)} WHERE id = '{job_id}';"
        logger.info(f"[DEBUG] Executing SQL: {update_sql}")

        cursor.execute(update_sql)
        conn.commit()
        logger.info(f"[DEBUG] Database update successful, {cursor.rowcount} rows affected")

        cursor.close()
        conn.close()

    except Exception as e:
        logger.error(f"[DEBUG] Error updating job status in database: {str(e)}")
        # Fall back to mock update
        logger.info(f"[DEBUG] MOCK JOB UPDATE: Setting job {job_id} status to '{status}'")
        for key, value in kwargs.items():
            logger.info(f"[DEBUG] MOCK JOB UPDATE: Setting {key} = {value}")


def get_job_details_sync(job_id: uuid.UUID):
    """
    Get job details synchronously for Celery tasks.
    This function directly queries the database using raw SQL to bypass the async requirements.
    """
    import psycopg2
    from app.core.config import settings

    logger.info(f"[DEBUG] Getting job details for {job_id}")

    # Extract database connection info from the async URL
    async_db_url = settings.DATABASE_URL

    # Convert the async URL to a standard PostgreSQL connection string
    try:
        if 'postgresql+asyncpg://' in async_db_url:
            db_url = async_db_url.replace('postgresql+asyncpg://', 'postgresql://')
            # Remove SSL parameter that psycopg2 doesn't understand
            if '?ssl=disable' in db_url:
                db_url = db_url.replace('?ssl=disable', '')
        else:
            db_url = async_db_url
    except Exception as e:
        logger.error(f"[DEBUG] Error converting database URL: {str(e)}")
        db_url = async_db_url

    try:
        # Connect to the database directly
        conn = psycopg2.connect(db_url)
        cursor = conn.cursor()

        # Query for job details
        query_sql = """
            SELECT id, status, analysis_type, file_path, original_filename,
                   file_size_bytes, duration_seconds, fps, processing_options,
                   job_metadata, created_at, started_at, completed_at, error_message
            FROM jobs
            WHERE id = %s;
        """

        cursor.execute(query_sql, (str(job_id),))
        row = cursor.fetchone()

        cursor.close()
        conn.close()

        if row:
            # Create a simple object to hold job data
            class JobData:
                def __init__(self, row_data):
                    self.id = uuid.UUID(row_data[0])
                    self.status = row_data[1]
                    self.analysis_type = row_data[2]
                    self.file_path = row_data[3]
                    self.original_filename = row_data[4]
                    self.file_size_bytes = row_data[5]
                    self.duration_seconds = row_data[6]
                    self.fps = row_data[7]
                    self.processing_options = row_data[8]
                    self.metadata = row_data[9]
                    self.created_at = row_data[10]
                    self.started_at = row_data[11]
                    self.completed_at = row_data[12]
                    self.error_message = row_data[13]

            job = JobData(row)
            logger.info(f"[DEBUG] Found job: {job.id}, status: {job.status}, file: {job.file_path}")
            return job
        else:
            logger.warning(f"[DEBUG] Job {job_id} not found in database")
            return None

    except Exception as e:
        logger.error(f"[DEBUG] Error getting job details from database: {str(e)}")
        return None



async def save_results_to_db_async(job_id: uuid.UUID, processed_frames: int, poses_detected: int, detection_rate: float,
                              pose_events: List[Dict[str, Any]], angle_stats: List[Dict[str, Any]], reba_scores: Dict[str, Any]):
    """
    Save analysis results to the database asynchronously.
    This is the real implementation for production use.
    """
    async with AsyncSessionLocal() as db:
        # Save REBA analysis results
        # (Implementation would depend on your database schema)

        # Update job status
        await job_crud.update_job_status(
            db,
            job_id,
            "completed",
            reba_score=reba_scores.get("final_reba_score")
        )

        logger.info(f"Saved analysis results for job {job_id} to database")
