# Nginx configuration snippet for REBA API
# Add this to your existing nginx.conf or include it as a separate file

# Add this upstream block to your existing nginx.conf (outside server blocks)
upstream reba_api {
    server 127.0.0.1:9001;  # REBA API container
}

# Add these location blocks to your existing server block
# or create a new server block for a subdomain

# Option 1: Add to existing server block (path-based routing)
# Add these locations inside your existing server { } block:

    # REBA API endpoints
    location /reba/api/ {
        # Rate limiting for REBA API
        limit_req zone=api burst=20 nodelay;
        
        # Remove /reba prefix before forwarding
        rewrite ^/reba/api/(.*)$ /api/$1 break;
        
        proxy_pass http://reba_api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Timeouts for long-running requests
        proxy_connect_timeout 300s;
        proxy_send_timeout 300s;
        proxy_read_timeout 300s;
        
        # File upload size limit (500MB)
        client_max_body_size 500M;
        client_body_timeout 300s;
    }

    # REBA video upload with extended timeouts
    location /reba/api/v1/videos/upload {
        # Special rate limiting for uploads
        limit_req zone=upload burst=3 nodelay;
        
        rewrite ^/reba/api/(.*)$ /api/$1 break;
        
        proxy_pass http://reba_api;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Extended timeouts for video uploads
        proxy_connect_timeout 600s;
        proxy_send_timeout 600s;
        proxy_read_timeout 600s;
        
        # Large file upload settings
        client_max_body_size 500M;
        client_body_timeout 600s;
        client_header_timeout 300s;
    }

    # REBA static files (uploads)
    location /reba/uploads/ {
        alias /path/to/your/reba/uploads/;  # Update this path
        expires 1d;
        add_header Cache-Control "public, immutable";
    }

    # REBA health check
    location /reba/health {
        proxy_pass http://reba_api/api/v1/health;
        access_log off;
    }

# Option 2: Subdomain-based routing (create new server block)
# Uncomment and modify if you prefer subdomain routing:

# server {
#     listen 80;
#     server_name reba.yourdomain.com;  # Replace with your subdomain
#     
#     # Security headers
#     add_header X-Frame-Options DENY;
#     add_header X-Content-Type-Options nosniff;
#     add_header X-XSS-Protection "1; mode=block";
#     
#     # File upload size limit (500MB)
#     client_max_body_size 500M;
#     client_body_timeout 300s;
#     client_header_timeout 300s;
#     
#     # API endpoints (no prefix needed)
#     location /api/ {
#         limit_req zone=api burst=20 nodelay;
#         proxy_pass http://reba_api;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#         
#         proxy_connect_timeout 300s;
#         proxy_send_timeout 300s;
#         proxy_read_timeout 300s;
#     }
#     
#     # Video upload endpoint
#     location /api/v1/videos/upload {
#         limit_req zone=upload burst=3 nodelay;
#         proxy_pass http://reba_api;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#         
#         proxy_connect_timeout 600s;
#         proxy_send_timeout 600s;
#         proxy_read_timeout 600s;
#     }
#     
#     # Static files
#     location /uploads/ {
#         alias /path/to/your/reba/uploads/;
#         expires 1d;
#         add_header Cache-Control "public, immutable";
#     }
#     
#     # Health check
#     location /health {
#         proxy_pass http://reba_api/api/v1/health;
#         access_log off;
#     }
#     
#     # Root redirect to docs
#     location / {
#         proxy_pass http://reba_api;
#         proxy_set_header Host $host;
#         proxy_set_header X-Real-IP $remote_addr;
#         proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
#         proxy_set_header X-Forwarded-Proto $scheme;
#     }
# }

# Rate limiting zones (add to http block if not already present)
# limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
# limit_req_zone $binary_remote_addr zone=upload:10m rate=1r/s;
