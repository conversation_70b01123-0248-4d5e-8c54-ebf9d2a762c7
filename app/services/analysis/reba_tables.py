"""
REBA (Rapid Entire Body Assessment) lookup tables and scoring functions.
These tables are based on the official REBA methodology.
"""

# Table A: Combines Neck + Trunk + Legs scores → Posture Score A
TABLE_A = [
    # Neck = 1
    [
        [1, 2, 3, 4],  # Trunk = 1
        [2, 3, 4, 5],  # Trunk = 2
        [2, 4, 5, 6],  # Trunk = 3
        [3, 5, 6, 7],  # Trunk = 4
        [4, 6, 7, 8]   # Trunk = 5
    ],
    # Neck = 2
    [
        [1, 3, 4, 5],  # Trunk = 1
        [2, 4, 5, 6],  # Trunk = 2
        [3, 5, 6, 7],  # Trunk = 3
        [4, 6, 7, 8],  # Trunk = 4
        [5, 7, 8, 9]   # Trunk = 5
    ],
    # Neck = 3
    [
        [3, 4, 5, 6],  # Trunk = 1
        [3, 5, 6, 7],  # Trunk = 2
        [4, 6, 7, 8],  # Trunk = 3
        [5, 7, 8, 9],  # Trunk = 4
        [6, 8, 9, 9]   # Trunk = 5
    ]
]

# Table B: Combines Upper Arm + Lower Arm + Wrist scores → Posture Score B
TABLE_B = [
    # Upper Arm = 1
    [
        [1, 2, 2],  # Lower Arm = 1
        [1, 2, 3]   # Lower Arm = 2
    ],
    # Upper Arm = 2
    [
        [1, 2, 3],  # Lower Arm = 1
        [2, 3, 4]   # Lower Arm = 2
    ],
    # Upper Arm = 3
    [
        [3, 4, 5],  # Lower Arm = 1
        [3, 4, 5]   # Lower Arm = 2
    ],
    # Upper Arm = 4
    [
        [4, 5, 5],  # Lower Arm = 1
        [4, 5, 6]   # Lower Arm = 2
    ],
    # Upper Arm = 5
    [
        [6, 7, 8],  # Lower Arm = 1
        [6, 7, 8]   # Lower Arm = 2
    ],
    # Upper Arm = 6
    [
        [7, 8, 8],  # Lower Arm = 1
        [7, 8, 9]   # Lower Arm = 2
    ]
]

# Table C: Combines Posture Score A + Posture Score B → Final REBA Score
TABLE_C = [
    [1, 1, 1, 2, 3, 3, 4, 5, 6],       # Score A = 1
    [1, 2, 2, 3, 4, 4, 5, 6, 6],       # Score A = 2
    [1, 2, 2, 3, 4, 5, 6, 7, 7],       # Score A = 3
    [2, 3, 3, 4, 5, 6, 7, 8, 8],       # Score A = 4
    [3, 4, 4, 5, 6, 7, 8, 8, 9],       # Score A = 5
    [3, 4, 5, 6, 7, 8, 8, 9, 9],       # Score A = 6
    [4, 5, 6, 7, 8, 8, 9, 9, 9],       # Score A = 7
    [5, 6, 7, 8, 8, 9, 9, 9, 9],       # Score A = 8
    [6, 6, 7, 8, 9, 9, 9, 9, 9],       # Score A = 9
    [7, 7, 8, 9, 9, 9, 9, 9, 9],       # Score A = 10
    [8, 8, 8, 9, 9, 9, 9, 9, 9],       # Score A = 11
    [9, 9, 9, 9, 9, 9, 9, 9, 9]        # Score A = 12
]


def score_neck(angle: float, twisted: bool = False, side_bending: bool = False) -> dict:
    """
    Calculate the REBA score for neck position.
    
    Args:
        angle: Neck angle in degrees
        twisted: Whether the neck is twisted
        side_bending: Whether the neck is bent to the side
    
    Returns:
        Dictionary with base score, modifiers, and final score
    """
    # Determine base score
    if 0 <= angle <= 20:
        base_score = 1
        category = "neutral_0_20_deg"
    else:
        base_score = 2
        if angle > 20:
            category = "flexion_over_20_deg"
        else:
            category = "extension"
    
    # Apply modifiers
    modifiers = {}
    if twisted:
        modifiers["twisted"] = 1
    else:
        modifiers["twisted"] = 0
        
    if side_bending:
        modifiers["side_bending"] = 1
    else:
        modifiers["side_bending"] = 0
    
    # Calculate final score
    final_score = base_score + sum(modifiers.values())
    
    return {
        "base_score": base_score,
        "modifiers": modifiers,
        "final_score": final_score,
        "category": category
    }


def score_trunk(angle: float, twisted: bool = False, side_bending: bool = False) -> dict:
    """
    Calculate the REBA score for trunk position.
    
    Args:
        angle: Trunk angle in degrees
        twisted: Whether the trunk is twisted
        side_bending: Whether the trunk is bent to the side
    
    Returns:
        Dictionary with base score, modifiers, and final score
    """
    # Determine base score
    if angle == 0:
        base_score = 1
        category = "upright_0_deg"
    elif 0 < angle <= 20:
        base_score = 2
        category = "flexion_0_20_deg"
    elif 20 < angle <= 60:
        base_score = 3
        category = "flexion_20_60_deg"
    else:
        base_score = 4
        if angle > 60:
            category = "flexion_over_60_deg"
        else:
            category = "extension"
    
    # Apply modifiers
    modifiers = {}
    if twisted:
        modifiers["twisted"] = 1
    else:
        modifiers["twisted"] = 0
        
    if side_bending:
        modifiers["side_bending"] = 1
    else:
        modifiers["side_bending"] = 0
    
    # Calculate final score
    final_score = base_score + sum(modifiers.values())
    
    return {
        "base_score": base_score,
        "modifiers": modifiers,
        "final_score": final_score,
        "category": category
    }


def score_legs(angle: float, one_leg_raised: bool = False) -> dict:
    """
    Calculate the REBA score for legs position.
    
    Args:
        angle: Leg angle in degrees
        one_leg_raised: Whether one leg is raised
    
    Returns:
        Dictionary with score and category
    """
    if one_leg_raised:
        score = 2
        category = "one_leg_raised"
    else:
        score = 1
        category = "both_legs_normal"
    
    return {
        "score": score,
        "category": category
    }


def score_upper_arm(angle: float, shoulder_raised: bool = False, 
                   abducted: bool = False, supported: bool = False) -> dict:
    """
    Calculate the REBA score for upper arm position.
    
    Args:
        angle: Upper arm angle in degrees
        shoulder_raised: Whether the shoulder is raised
        abducted: Whether the arm is abducted
        supported: Whether the arm is supported
    
    Returns:
        Dictionary with base score, modifiers, and final score
    """
    # Determine base score
    if -20 <= angle <= 20:
        base_score = 1
        category = "drop_20_to_plus_20"
    elif (angle < -20) or (20 < angle <= 45):
        base_score = 2
        category = "extension_20_or_flexion_20_45"
    elif 45 < angle <= 90:
        base_score = 3
        category = "flexion_45_90"
    else:
        base_score = 4
        category = "flexion_over_90"
    
    # Apply modifiers
    modifiers = {}
    if shoulder_raised:
        modifiers["shoulder_raised"] = 1
    else:
        modifiers["shoulder_raised"] = 0
        
    if abducted:
        modifiers["abducted"] = 1
    else:
        modifiers["abducted"] = 0
        
    if supported:
        modifiers["supported"] = -1
    else:
        modifiers["supported"] = 0
    
    # Calculate final score
    final_score = base_score + sum(modifiers.values())
    if final_score < 1:
        final_score = 1
    
    return {
        "base_score": base_score,
        "modifiers": modifiers,
        "final_score": final_score,
        "category": category
    }


def score_lower_arm(angle: float) -> dict:
    """
    Calculate the REBA score for lower arm position.
    
    Args:
        angle: Lower arm angle in degrees
    
    Returns:
        Dictionary with score and category
    """
    if 60 <= angle <= 100:
        score = 1
        category = "flexion_60_100"
    else:
        score = 2
        category = "flexion_under_60_over_100"
    
    return {
        "score": score,
        "category": category
    }


def score_wrist(angle: float, bent_from_midline: bool = False) -> dict:
    """
    Calculate the REBA score for wrist position.
    
    Args:
        angle: Wrist angle in degrees
        bent_from_midline: Whether the wrist is bent from the midline
    
    Returns:
        Dictionary with base score, modifiers, and final score
    """
    # Determine base score
    if 0 <= angle <= 15:
        base_score = 1
        category = "neutral_0_15_deg"
    else:
        base_score = 2
        category = "flexion_extension_over_15"
    
    # Apply modifiers
    modifiers = {}
    if bent_from_midline:
        modifiers["bent_from_midline"] = 1
    else:
        modifiers["bent_from_midline"] = 0
    
    # Calculate final score
    final_score = base_score + sum(modifiers.values())
    
    return {
        "base_score": base_score,
        "modifiers": modifiers,
        "final_score": final_score,
        "category": category
    }


def lookup_table_a(neck_score: int, trunk_score: int, legs_score: int) -> int:
    """
    Look up the posture score A from Table A.
    
    Args:
        neck_score: Neck score (1-3)
        trunk_score: Trunk score (1-5)
        legs_score: Legs score (1-4)
    
    Returns:
        Posture score A (1-9)
    """
    # Ensure scores are within valid ranges
    neck_score = max(1, min(3, neck_score))
    trunk_score = max(1, min(5, trunk_score))
    legs_score = max(1, min(4, legs_score))
    
    # Look up score in Table A
    return TABLE_A[neck_score - 1][trunk_score - 1][legs_score - 1]


def lookup_table_b(upper_arm_score: int, lower_arm_score: int, wrist_score: int) -> int:
    """
    Look up the posture score B from Table B.
    
    Args:
        upper_arm_score: Upper arm score (1-6)
        lower_arm_score: Lower arm score (1-2)
        wrist_score: Wrist score (1-3)
    
    Returns:
        Posture score B (1-9)
    """
    # Ensure scores are within valid ranges
    upper_arm_score = max(1, min(6, upper_arm_score))
    lower_arm_score = max(1, min(2, lower_arm_score))
    wrist_score = max(1, min(3, wrist_score))
    
    # Look up score in Table B
    return TABLE_B[upper_arm_score - 1][lower_arm_score - 1][wrist_score - 1]


def lookup_table_c(score_a: int, score_b: int) -> int:
    """
    Look up the REBA score from Table C.
    
    Args:
        score_a: Posture score A (1-12)
        score_b: Posture score B (1-9)
    
    Returns:
        REBA score (1-9)
    """
    # Ensure scores are within valid ranges
    score_a = max(1, min(12, score_a))
    score_b = max(1, min(9, score_b))
    
    # Look up score in Table C
    return TABLE_C[score_a - 1][score_b - 1]


def get_risk_level(score: int) -> str:
    """
    Get the risk level based on the REBA score.
    
    Args:
        score: REBA score (1-15+)
    
    Returns:
        Risk level (negligible, low, medium, high, very_high)
    """
    if score == 1:
        return "negligible"
    elif score in [2, 3]:
        return "low"
    elif score in [4, 5, 6, 7]:
        return "medium"
    elif score in [8, 9, 10]:
        return "high"
    else:
        return "very_high"
