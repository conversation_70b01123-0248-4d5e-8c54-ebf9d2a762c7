# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual Environment
venv/
env/
ENV/
.env
.venv
env.bak/
venv.bak/

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store

# Docker
.dockerignore
docker-compose.override.yml

# Database
*.sqlite3
*.db

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Alembic
alembic/versions/__pycache__/

# Coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/
.pytest_cache/

# Jupyter Notebook
.ipynb_checkpoints

# mypy
.mypy_cache/

# pytest
.pytest_cache/

# Environments
.env.local
.env.development.local
.env.test.local
.env.production.local

# Project specific
.env
uploads/*
