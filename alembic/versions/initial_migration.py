"""Initial migration

Revision ID: 1a2b3c4d5e6f
Revises: 
Create Date: 2025-05-28 17:50:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '1a2b3c4d5e6f'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # Create jobs table
    op.create_table(
        'jobs',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('status', sa.String(20), nullable=False, server_default='queued'),
        sa.Column('analysis_type', sa.String(10), nullable=False, server_default='reba'),
        sa.Column('created_at', sa.TIMESTAMP, server_default=sa.text('NOW()')),
        sa.Column('started_at', sa.TIMESTAMP, nullable=True),
        sa.Column('completed_at', sa.TIMESTAMP, nullable=True),
        sa.Column('file_path', sa.String(500), nullable=True),
        sa.Column('original_filename', sa.String(255), nullable=True),
        sa.Column('file_size_bytes', sa.Integer, nullable=True),
        sa.Column('duration_seconds', sa.Float, nullable=True),
        sa.Column('fps', sa.Float, nullable=True),
        sa.Column('error_message', sa.String, nullable=True),
        sa.Column('processing_options', postgresql.JSONB, nullable=True),
        sa.Column('metadata', postgresql.JSONB, nullable=True)
    )
    
    # Create reba_results table
    op.create_table(
        'reba_results',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('job_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('jobs.id', ondelete='CASCADE')),
        sa.Column('overall_risk_level', sa.String(10), nullable=True),
        sa.Column('total_frames', sa.Integer, nullable=True),
        sa.Column('poses_detected', sa.Integer, nullable=True),
        sa.Column('detection_rate', sa.Float, nullable=True),
        sa.Column('processing_time_seconds', sa.Float, nullable=True),
        sa.Column('created_at', sa.TIMESTAMP, server_default=sa.text('NOW()'))
    )
    
    # Create reba_detailed_scores table
    op.create_table(
        'reba_detailed_scores',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('job_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('jobs.id', ondelete='CASCADE')),
        
        # Group A components
        sa.Column('neck_base_score', sa.Integer, nullable=False),
        sa.Column('neck_twist_modifier', sa.Integer, server_default='0'),
        sa.Column('neck_side_bending_modifier', sa.Integer, server_default='0'),
        sa.Column('neck_final_score', sa.Integer, nullable=False),
        sa.Column('neck_analysis', postgresql.JSONB, nullable=True),
        
        sa.Column('trunk_base_score', sa.Integer, nullable=False),
        sa.Column('trunk_twist_modifier', sa.Integer, server_default='0'),
        sa.Column('trunk_side_bending_modifier', sa.Integer, server_default='0'),
        sa.Column('trunk_final_score', sa.Integer, nullable=False),
        sa.Column('trunk_analysis', postgresql.JSONB, nullable=True),
        
        sa.Column('legs_score', sa.Integer, nullable=False),
        sa.Column('legs_analysis', postgresql.JSONB, nullable=True),
        
        sa.Column('posture_score_a', sa.Integer, nullable=False),
        
        # Group B components
        sa.Column('upper_arm_base_score', sa.Integer, nullable=False),
        sa.Column('upper_arm_shoulder_modifier', sa.Integer, server_default='0'),
        sa.Column('upper_arm_abduction_modifier', sa.Integer, server_default='0'),
        sa.Column('upper_arm_support_modifier', sa.Integer, server_default='0'),
        sa.Column('upper_arm_final_score', sa.Integer, nullable=False),
        sa.Column('upper_arm_analysis', postgresql.JSONB, nullable=True),
        
        sa.Column('lower_arm_score', sa.Integer, nullable=False),
        sa.Column('lower_arm_analysis', postgresql.JSONB, nullable=True),
        
        sa.Column('wrist_base_score', sa.Integer, nullable=False),
        sa.Column('wrist_deviation_modifier', sa.Integer, server_default='0'),
        sa.Column('wrist_final_score', sa.Integer, nullable=False),
        sa.Column('wrist_analysis', postgresql.JSONB, nullable=True),
        
        sa.Column('posture_score_b', sa.Integer, nullable=False),
        
        # Force and activity modifiers
        sa.Column('force_load_score', sa.Integer, server_default='0'),
        sa.Column('coupling_score', sa.Integer, server_default='0'),
        sa.Column('activity_score', sa.Integer, server_default='0'),
        
        # Final scores
        sa.Column('final_reba_score', sa.Integer, nullable=False),
        sa.Column('risk_level', sa.String(20), nullable=False),
        
        sa.Column('created_at', sa.TIMESTAMP, server_default=sa.text('NOW()'))
    )
    
    # Create pose_duration_events table
    op.create_table(
        'pose_duration_events',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('job_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('jobs.id', ondelete='CASCADE')),
        sa.Column('body_part', sa.String(30), nullable=False),
        sa.Column('pose_category', sa.String(50), nullable=False),
        sa.Column('start_time_seconds', sa.Float, nullable=False),
        sa.Column('end_time_seconds', sa.Float, nullable=False),
        sa.Column('duration_seconds', sa.Float, nullable=False),
        sa.Column('angle_range', sa.String(30), nullable=True),
        sa.Column('base_reba_score', sa.Integer, nullable=True),
        sa.Column('modifiers', postgresql.JSONB, server_default='{}'),
        sa.Column('final_score', sa.Integer, nullable=True),
        sa.Column('created_at', sa.TIMESTAMP, server_default=sa.text('NOW()'))
    )
    
    # Create angle_summary_stats table
    op.create_table(
        'angle_summary_stats',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, server_default=sa.text('gen_random_uuid()')),
        sa.Column('job_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('jobs.id', ondelete='CASCADE')),
        sa.Column('body_part', sa.String(30), nullable=False),
        sa.Column('avg_angle', sa.Float, nullable=True),
        sa.Column('min_angle', sa.Float, nullable=True),
        sa.Column('max_angle', sa.Float, nullable=True),
        sa.Column('std_deviation', sa.Float, nullable=True),
        sa.Column('total_measurements', sa.Integer, nullable=True),
        sa.Column('created_at', sa.TIMESTAMP, server_default=sa.text('NOW()'))
    )
    
    # Create frame_measurements table (optional - for debugging/detailed analysis)
    op.create_table(
        'frame_measurements',
        sa.Column('id', sa.BigInteger, primary_key=True),
        sa.Column('job_id', postgresql.UUID(as_uuid=True), sa.ForeignKey('jobs.id', ondelete='CASCADE')),
        sa.Column('frame_number', sa.Integer, nullable=False),
        sa.Column('timestamp_seconds', sa.Float, nullable=False),
        sa.Column('reba_components', postgresql.JSONB, nullable=False),
        sa.Column('final_reba_score', sa.Integer, nullable=True),
        sa.Column('pose_detected', sa.Boolean, server_default='true'),
        sa.Column('created_at', sa.TIMESTAMP, server_default=sa.text('NOW()'))
    )
    
    # Create indexes
    op.create_index('idx_jobs_status', 'jobs', ['status'])
    op.create_index('idx_jobs_created_at', 'jobs', [sa.text('created_at DESC')])
    
    op.create_index('idx_reba_results_job_id', 'reba_results', ['job_id'])
    op.create_index('idx_reba_detailed_job_id', 'reba_detailed_scores', ['job_id'])
    
    op.create_index('idx_pose_duration_job_body', 'pose_duration_events', ['job_id', 'body_part'])
    op.create_index('idx_pose_duration_job_time', 'pose_duration_events', ['job_id', 'start_time_seconds'])
    op.create_index('idx_pose_duration_category', 'pose_duration_events', ['job_id', 'pose_category'])
    
    op.create_index('idx_angle_summary_job_body', 'angle_summary_stats', ['job_id', 'body_part'])
    
    # Create JSONB indexes
    op.execute(
        'CREATE INDEX idx_reba_detailed_components ON reba_detailed_scores USING GIN (neck_analysis, trunk_analysis, legs_analysis, upper_arm_analysis, lower_arm_analysis, wrist_analysis)'
    )
    op.execute(
        'CREATE INDEX idx_frame_measurements_components ON frame_measurements USING GIN (reba_components)'
    )
    op.execute(
        'CREATE INDEX idx_pose_duration_modifiers ON pose_duration_events USING GIN (modifiers)'
    )


def downgrade():
    # Drop tables in reverse order
    op.drop_table('frame_measurements')
    op.drop_table('angle_summary_stats')
    op.drop_table('pose_duration_events')
    op.drop_table('reba_detailed_scores')
    op.drop_table('reba_results')
    op.drop_table('jobs')
