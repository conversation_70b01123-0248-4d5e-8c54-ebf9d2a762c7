# Production Environment Configuration
# Copy this to .env and update values for production deployment

# API Settings
API_V1_STR=/api/v1
PROJECT_NAME=REBA Video Analysis API
SECRET_KEY=CHANGE_THIS_TO_A_SECURE_SECRET_KEY_IN_PRODUCTION
ACCESS_TOKEN_EXPIRE_MINUTES=60
ALGORITHM=HS256
DEBUG=False

# Database Settings (Update with your production database)
DATABASE_URL=postgresql+asyncpg://reba_user:CHANGE_PASSWORD@postgres:5432/reba_db?ssl=disable
ASYNC_DATABASE_URL=postgresql+asyncpg://reba_user:CHAN<PERSON>_PASSWORD@postgres:5432/reba_db?ssl=disable

# Redis Settings
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_URL=redis://redis:6379/0

# Celery Settings
CELERY_BROKER_URL=redis://redis:6379/0
CELERY_RESULT_BACKEND=redis://redis:6379/0

# File Storage Settings
UPLOAD_DIR=/app/uploads
# 500MB in bytes
MAX_FILE_SIZE=524288000
# 30 minutes in seconds (1800 seconds)
MAX_VIDEO_DURATION=1800

# Processing Settings
DETECTION_CONFIDENCE=0.87
MODEL_COMPLEXITY=2
FRAME_RATE_REDUCTION=1

# MediaPipe Settings
MEDIAPIPE_DISABLE_GPU=1
GLOG_logtostderr=1

# Security Settings (for production)
ALLOWED_HOSTS=["*"]  # Update with your domain
CORS_ORIGINS=["*"]   # Update with your frontend domain
