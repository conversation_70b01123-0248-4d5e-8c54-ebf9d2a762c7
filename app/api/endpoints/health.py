from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text
from app.db.database import get_db
from app.core.config import settings
import redis
from datetime import datetime
import psutil
import os

router = APIRouter()


@router.get("/", status_code=status.HTTP_200_OK)
async def health_check(db: AsyncSession = Depends(get_db)):
    """
    Check the health of the API and its dependencies.
    """
    # Check database connection
    try:
        # Execute a simple query using SQLAlchemy's text() function
        await db.execute(text("SELECT 1"))
        db_status = "healthy"
    except Exception as e:
        db_status = f"unhealthy: {str(e)}"
    
    # Check Redis connection
    try:
        r = redis.Redis(
            host=settings.REDIS_HOST,
            port=settings.REDIS_PORT,
            socket_connect_timeout=2
        )
        r.ping()
        redis_status = "healthy"
    except Exception as e:
        redis_status = f"unhealthy: {str(e)}"
    
    # Get system stats
    cpu_percent = psutil.cpu_percent(interval=0.1)
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('/')
    
    return {
        "status": "ok",
        "timestamp": datetime.utcnow().isoformat(),
        "version": "1.0.0",
        "dependencies": {
            "database": db_status,
            "redis": redis_status
        },
        "system": {
            "cpu_usage_percent": cpu_percent,
            "memory_usage_percent": memory.percent,
            "disk_usage_percent": disk.percent
        }
    }


@router.get("/stats", status_code=status.HTTP_200_OK)
async def system_stats():
    """
    Get detailed system statistics.
    """
    # Get CPU stats
    cpu_percent = psutil.cpu_percent(interval=0.1)
    cpu_count = psutil.cpu_count()
    cpu_freq = psutil.cpu_freq()
    
    # Get memory stats
    memory = psutil.virtual_memory()
    
    # Get disk stats
    disk = psutil.disk_usage('/')
    
    # Get process stats
    process = psutil.Process(os.getpid())
    process_memory = process.memory_info()
    
    return {
        "timestamp": datetime.utcnow().isoformat(),
        "system": {
            "cpu": {
                "usage_percent": cpu_percent,
                "count": cpu_count,
                "frequency_mhz": cpu_freq.current if cpu_freq else None
            },
            "memory": {
                "total_bytes": memory.total,
                "available_bytes": memory.available,
                "used_bytes": memory.used,
                "usage_percent": memory.percent
            },
            "disk": {
                "total_bytes": disk.total,
                "free_bytes": disk.free,
                "used_bytes": disk.used,
                "usage_percent": disk.percent
            }
        },
        "process": {
            "memory_rss_bytes": process_memory.rss,
            "memory_vms_bytes": process_memory.vms,
            "cpu_percent": process.cpu_percent(interval=0.1),
            "threads": process.num_threads()
        }
    }
