#!/usr/bin/env python3
"""
Test the full video upload and processing workflow
"""
import requests
import time
import logging
import os

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

BASE_URL = "http://localhost:9000/api/v1"

def test_full_workflow():
    """Test uploading a video and checking its processing status"""
    
    # Use an existing video file for testing
    video_files = [
        "./uploads/237e7694-7c4f-4d5a-b81a-4ba2fcc63625/237e7694-7c4f-4d5a-b81a-4ba2fcc63625.mp4",
        "./uploads/212bb84f-46d1-4d09-af98-66166fdf4a05/212bb84f-46d1-4d09-af98-66166fdf4a05.mp4",
        "./uploads/3c4cabbd-18ba-41f3-a915-bbac175d6047/3c4cabbd-18ba-41f3-a915-bbac175d6047.mp4"
    ]
    
    # Find the first existing video file
    test_video = None
    for video_file in video_files:
        if os.path.exists(video_file):
            test_video = video_file
            break
    
    if not test_video:
        logger.error("No test video file found")
        return
    
    logger.info(f"Testing with video: {test_video}")
    
    # Step 1: Upload video
    logger.info("Step 1: Uploading video...")
    
    try:
        with open(test_video, 'rb') as f:
            files = {'file': (os.path.basename(test_video), f, 'video/mp4')}
            data = {
                'analysis_type': 'reba',
                'options': '{"detection_confidence": 0.87, "model_complexity": 2}',
                'metadata': '{"test": "full_workflow"}'
            }
            
            response = requests.post(f"{BASE_URL}/videos/upload", files=files, data=data)
            
            if response.status_code == 200:
                result = response.json()
                job_id = result['id']
                logger.info(f"✓ Video uploaded successfully. Job ID: {job_id}")
                logger.info(f"  Status: {result['status']}")
                logger.info(f"  File info: {result['file_info']}")
            else:
                logger.error(f"✗ Video upload failed: {response.status_code}")
                logger.error(f"Response: {response.text}")
                return
                
    except Exception as e:
        logger.error(f"✗ Error uploading video: {e}")
        return
    
    # Step 2: Check job status
    logger.info(f"\nStep 2: Monitoring job status for {job_id}...")
    
    max_wait_time = 60  # 1 minute
    check_interval = 5  # 5 seconds
    start_time = time.time()
    
    while time.time() - start_time < max_wait_time:
        try:
            response = requests.get(f"{BASE_URL}/jobs/{job_id}")
            
            if response.status_code == 200:
                job_status = response.json()
                logger.info(f"  Job status: {job_status['status']}")
                
                if job_status['status'] == 'completed':
                    logger.info("✓ Job completed successfully!")
                    logger.info(f"  REBA Score: {job_status.get('reba_score', 'N/A')}")
                    if 'enhanced_response' in job_status:
                        enhanced = job_status['enhanced_response']
                        if 'analysis_summary' in enhanced:
                            summary = enhanced['analysis_summary']
                            logger.info(f"  Risk Level: {summary.get('overall_risk_level', 'N/A')}")
                            logger.info(f"  Frames Analyzed: {summary.get('frames_analyzed', 'N/A')}")
                            logger.info(f"  Detection Rate: {summary.get('detection_rate', 'N/A')}%")
                    break
                elif job_status['status'] == 'failed':
                    logger.error("✗ Job failed!")
                    logger.error(f"  Error: {job_status.get('error_message', 'Unknown error')}")
                    break
                elif job_status['status'] in ['queued', 'processing']:
                    logger.info(f"  Job is {job_status['status']}, waiting...")
                    time.sleep(check_interval)
                else:
                    logger.warning(f"  Unknown status: {job_status['status']}")
                    time.sleep(check_interval)
            else:
                logger.error(f"✗ Error checking job status: {response.status_code}")
                logger.error(f"Response: {response.text}")
                break
                
        except Exception as e:
            logger.error(f"✗ Error checking job status: {e}")
            break
    
    else:
        logger.warning("⚠ Job monitoring timed out")

if __name__ == "__main__":
    test_full_workflow()
