# Production Docker Compose Configuration
# Use this for production deployment on EC2

version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: reba_db
      POSTGRES_USER: reba_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-reba_password}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U reba_user -d reba_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - reba_network

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    restart: unless-stopped
    networks:
      - reba_network

  web:
    build: 
      context: .
      dockerfile: Dockerfile.prod
    ports:
      - "9000:8000"
    environment:
      - DATABASE_URL=postgresql+asyncpg://reba_user:${POSTGRES_PASSWORD:-reba_password}@postgres:5432/reba_db?ssl=disable
      - ASYNC_DATABASE_URL=postgresql+asyncpg://reba_user:${POSTGRES_PASSWORD:-reba_password}@postgres:5432/reba_db?ssl=disable
      - REDIS_URL=redis://redis:6379/0
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY:-temporarysecretkeyreplaceinproduction}
      - DEBUG=False
      - MEDIAPIPE_DISABLE_GPU=1
      - GLOG_logtostderr=1
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./uploads:/app/uploads
    restart: unless-stopped
    networks:
      - reba_network

  celery:
    build: 
      context: .
      dockerfile: Dockerfile.prod
    command: celery -A app.core.celery_app worker --loglevel=info --concurrency=2
    environment:
      - DATABASE_URL=postgresql+asyncpg://reba_user:${POSTGRES_PASSWORD:-reba_password}@postgres:5432/reba_db?ssl=disable
      - ASYNC_DATABASE_URL=postgresql+asyncpg://reba_user:${POSTGRES_PASSWORD:-reba_password}@postgres:5432/reba_db?ssl=disable
      - REDIS_URL=redis://redis:6379/0
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY:-temporarysecretkeyreplaceinproduction}
      - MEDIAPIPE_DISABLE_GPU=1
      - GLOG_logtostderr=1
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./uploads:/app/uploads
    restart: unless-stopped
    networks:
      - reba_network

  # Optional: Nginx reverse proxy for production
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl  # For SSL certificates
    depends_on:
      - web
    restart: unless-stopped
    networks:
      - reba_network

volumes:
  postgres_data:
  redis_data:

networks:
  reba_network:
    driver: bridge
