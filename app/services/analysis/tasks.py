from app.core.celery_app import celery_app
from app.services.analysis.reba_processor import process_video_for_reba
from app.models.job import Job
from app.db.database import AsyncSessionLocal
from sqlalchemy.ext.asyncio import AsyncSession
import asyncio
import logging

logger = logging.getLogger(__name__)

@celery_app.task(name="process_video_task")
def process_video_task(job_id: int, video_path: str):
    """
    Celery task to process a video for REBA analysis.
    
    Args:
        job_id: The ID of the job in the database
        video_path: Path to the video file to process
    """
    logger.info(f"Starting video processing task for job_id={job_id}, video_path={video_path}")
    
    try:
        # Create an event loop for async operations
        loop = asyncio.get_event_loop()
        
        # Update job status to processing
        async def update_job_status_to_processing():
            async with AsyncSessionLocal() as session:
                await _update_job_status(session, job_id, "processing")
        
        loop.run_until_complete(update_job_status_to_processing())
        
        # Process the video
        results = process_video_for_reba(video_path, job_id, {})
        
        # Update job with results
        async def update_job_with_results():
            async with AsyncSessionLocal() as session:
                await _update_job_status(session, job_id, "completed", results)
        
        loop.run_until_complete(update_job_with_results())
        
        logger.info(f"Video processing completed successfully for job_id={job_id}")
        return {"status": "success", "job_id": job_id}
    
    except Exception as e:
        logger.error(f"Error processing video for job_id={job_id}: {str(e)}")
        
        # Update job status to failed
        try:
            loop = asyncio.get_event_loop()
            
            async def update_job_status_to_failed():
                async with AsyncSessionLocal() as session:
                    await _update_job_status(session, job_id, "failed", error=str(e))
            
            loop.run_until_complete(update_job_status_to_failed())
        except Exception as inner_e:
            logger.error(f"Failed to update job status: {str(inner_e)}")
        
        return {"status": "error", "job_id": job_id, "error": str(e)}

async def _update_job_status(session: AsyncSession, job_id: int, status: str, results=None, error=None):
    """Update the job status in the database"""
    job = await session.get(Job, job_id)
    if job:
        job.status = status
        if results:
            job.results = results
        if error:
            job.error = error
        await session.commit()
        return job
    return None
