from typing import Dict, Any, List
import uuid
import logging
from app.services.analysis.reba_tables import (
    score_neck, score_trunk, score_legs, score_upper_arm, 
    score_lower_arm, score_wrist, lookup_table_a, lookup_table_b, 
    lookup_table_c, get_risk_level
)

logger = logging.getLogger(__name__)


def calculate_reba_components(angles: Dict[str, float]) -> Dict[str, Any]:
    """
    Calculate REBA component scores from joint angles.
    
    Args:
        angles: Dictionary of joint angles
    
    Returns:
        Dictionary of REBA component scores
    """
    components = {}
    
    # Score neck
    if 'neck' in angles:
        # Simplified detection of neck twist and side bending
        # In a real implementation, this would use more sophisticated detection
        neck_twisted = False
        neck_side_bending = False
        components['neck'] = score_neck(angles['neck'], neck_twisted, neck_side_bending)
    else:
        components['neck'] = {'base_score': 1, 'modifiers': {'twisted': 0, 'side_bending': 0}, 'final_score': 1, 'category': 'neutral_0_20_deg'}
    
    # Score trunk
    if 'trunk' in angles:
        # Simplified detection of trunk twist and side bending
        trunk_twisted = False
        trunk_side_bending = False
        components['trunk'] = score_trunk(angles['trunk'], trunk_twisted, trunk_side_bending)
    else:
        components['trunk'] = {'base_score': 1, 'modifiers': {'twisted': 0, 'side_bending': 0}, 'final_score': 1, 'category': 'upright_0_deg'}
    
    # Score legs
    if 'legs' in angles:
        # Simplified detection of one leg raised
        one_leg_raised = False
        components['legs'] = score_legs(angles['legs'], one_leg_raised)
    else:
        components['legs'] = {'score': 1, 'category': 'both_legs_normal'}
    
    # Score upper arm (use the higher score from left and right)
    right_upper_arm = None
    left_upper_arm = None
    
    if 'upper_arm_right' in angles:
        # Simplified detection of shoulder raised, abduction, and support
        shoulder_raised = False
        abducted = False
        supported = False
        right_upper_arm = score_upper_arm(angles['upper_arm_right'], shoulder_raised, abducted, supported)
    
    if 'upper_arm_left' in angles:
        # Simplified detection of shoulder raised, abduction, and support
        shoulder_raised = False
        abducted = False
        supported = False
        left_upper_arm = score_upper_arm(angles['upper_arm_left'], shoulder_raised, abducted, supported)
    
    # Use the higher score
    if right_upper_arm and left_upper_arm:
        components['upper_arm'] = right_upper_arm if right_upper_arm['final_score'] >= left_upper_arm['final_score'] else left_upper_arm
    elif right_upper_arm:
        components['upper_arm'] = right_upper_arm
    elif left_upper_arm:
        components['upper_arm'] = left_upper_arm
    else:
        components['upper_arm'] = {'base_score': 1, 'modifiers': {'shoulder_raised': 0, 'abducted': 0, 'supported': 0}, 'final_score': 1, 'category': 'drop_20_to_plus_20'}
    
    # Score lower arm (use the higher score from left and right)
    right_lower_arm = None
    left_lower_arm = None
    
    if 'lower_arm_right' in angles:
        right_lower_arm = score_lower_arm(angles['lower_arm_right'])
    
    if 'lower_arm_left' in angles:
        left_lower_arm = score_lower_arm(angles['lower_arm_left'])
    
    # Use the higher score
    if right_lower_arm and left_lower_arm:
        components['lower_arm'] = right_lower_arm if right_lower_arm['score'] >= left_lower_arm['score'] else left_lower_arm
    elif right_lower_arm:
        components['lower_arm'] = right_lower_arm
    elif left_lower_arm:
        components['lower_arm'] = left_lower_arm
    else:
        components['lower_arm'] = {'score': 1, 'category': 'flexion_60_100'}
    
    # Score wrist (use the higher score from left and right)
    right_wrist = None
    left_wrist = None
    
    if 'wrist_right' in angles:
        # Simplified detection of wrist bent from midline
        bent_from_midline = False
        right_wrist = score_wrist(angles['wrist_right'], bent_from_midline)
    
    if 'wrist_left' in angles:
        # Simplified detection of wrist bent from midline
        bent_from_midline = False
        left_wrist = score_wrist(angles['wrist_left'], bent_from_midline)
    
    # Use the higher score
    if right_wrist and left_wrist:
        components['wrist'] = right_wrist if right_wrist['final_score'] >= left_wrist['final_score'] else left_wrist
    elif right_wrist:
        components['wrist'] = right_wrist
    elif left_wrist:
        components['wrist'] = left_wrist
    else:
        components['wrist'] = {'base_score': 1, 'modifiers': {'bent_from_midline': 0}, 'final_score': 1, 'category': 'neutral_0_15_deg'}
    
    # Calculate posture scores
    components['posture_score_a'] = lookup_table_a(
        components['neck']['final_score'],
        components['trunk']['final_score'],
        components['legs']['score']
    )
    
    components['posture_score_b'] = lookup_table_b(
        components['upper_arm']['final_score'],
        components['lower_arm']['score'],
        components['wrist']['final_score']
    )
    
    # Add force/load, coupling, and activity scores
    # In a real implementation, these would be detected from the video
    components['force_load_score'] = 0
    components['coupling_score'] = 0
    components['activity_score'] = 0
    
    return components


def calculate_final_reba_score(components: Dict[str, Any]) -> int:
    """
    Calculate the final REBA score from component scores.
    
    Args:
        components: Dictionary of REBA component scores
    
    Returns:
        Final REBA score
    """
    # Calculate score A
    score_a = components['posture_score_a']
    
    # Add force/load score
    score_a += components['force_load_score']
    
    # Calculate score B
    score_b = components['posture_score_b']
    
    # Add coupling score
    score_b += components['coupling_score']
    
    # Look up score C
    score_c = lookup_table_c(score_a, score_b)
    
    # Add activity score
    final_score = score_c + components['activity_score']
    
    return final_score


def consolidate_pose_events(frame_buffer: List[Dict[str, Any]], job_id: uuid.UUID) -> List[Dict[str, Any]]:
    """
    Consolidate frame-level data into pose duration events.
    
    Args:
        frame_buffer: List of frame data dictionaries
        job_id: Job ID
    
    Returns:
        List of pose duration events
    """
    # Minimum duration for a pose event (in seconds)
    min_duration = 0.5
    
    # Transition tolerance (in seconds)
    transition_tolerance = 0.2
    
    # Initialize pose events
    pose_events = []
    
    # Track current state for each body part
    current_states = {}
    
    # Process frames
    for i, frame in enumerate(frame_buffer):
        if not frame['pose_detected']:
            continue
        
        # Process each body part
        for body_part in ['neck', 'trunk', 'legs', 'upper_arm', 'lower_arm', 'wrist']:
            if body_part not in frame['reba_components']:
                continue
            
            # Get the current category and score
            component = frame['reba_components'][body_part]
            category = component.get('category')
            
            if not category:
                continue
            
            # Get the score
            if body_part in ['legs', 'lower_arm']:
                score = component['score']
            else:
                score = component['final_score']
            
            # Create a unique key for this body part and category
            state_key = f"{body_part}_{category}"
            
            # Check if this is a new state or continuation
            if state_key in current_states:
                # Update end time
                current_states[state_key]['end_frame'] = i
                current_states[state_key]['end_time'] = frame['timestamp_seconds']
            else:
                # Start a new state
                current_states[state_key] = {
                    'body_part': body_part,
                    'pose_category': category,
                    'start_frame': i,
                    'start_time': frame['timestamp_seconds'],
                    'end_frame': i,
                    'end_time': frame['timestamp_seconds'],
                    'score': score,
                    'component': component
                }
        
        # Check for expired states
        expired_keys = []
        for state_key, state in current_states.items():
            # Check if this state has expired (no update in the last transition_tolerance seconds)
            if frame['timestamp_seconds'] - state['end_time'] > transition_tolerance:
                expired_keys.append(state_key)
        
        # Process expired states
        for state_key in expired_keys:
            state = current_states[state_key]
            
            # Calculate duration
            duration = state['end_time'] - state['start_time']
            
            # Only keep events longer than the minimum duration
            if duration >= min_duration:
                # Create a pose event
                pose_event = {
                    'job_id': job_id,
                    'body_part': state['body_part'],
                    'pose_category': state['pose_category'],
                    'start_time_seconds': state['start_time'],
                    'end_time_seconds': state['end_time'],
                    'duration_seconds': duration,
                    'angle_range': state['pose_category'],
                    'base_reba_score': state['component'].get('base_score', state['score']),
                    'modifiers': state['component'].get('modifiers', {}),
                    'final_score': state['score']
                }
                
                pose_events.append(pose_event)
            
            # Remove the expired state
            del current_states[state_key]
    
    # Process any remaining states
    for state_key, state in current_states.items():
        # Calculate duration
        duration = state['end_time'] - state['start_time']
        
        # Only keep events longer than the minimum duration
        if duration >= min_duration:
            # Create a pose event
            pose_event = {
                'job_id': job_id,
                'body_part': state['body_part'],
                'pose_category': state['pose_category'],
                'start_time_seconds': state['start_time'],
                'end_time_seconds': state['end_time'],
                'duration_seconds': duration,
                'angle_range': state['pose_category'],
                'base_reba_score': state['component'].get('base_score', state['score']),
                'modifiers': state['component'].get('modifiers', {}),
                'final_score': state['score']
            }
            
            pose_events.append(pose_event)
    
    return pose_events


def calculate_angle_statistics(frame_buffer: List[Dict[str, Any]], job_id: uuid.UUID) -> List[Dict[str, Any]]:
    """
    Calculate angle statistics from frame data.
    
    Args:
        frame_buffer: List of frame data dictionaries
        job_id: Job ID
    
    Returns:
        List of angle statistics
    """
    import numpy as np
    
    # Initialize angle data
    angle_data = {}
    
    # Process frames
    for frame in frame_buffer:
        if not frame['pose_detected']:
            continue
        
        # Process each angle
        for angle_name, angle_value in frame['angles'].items():
            if angle_name not in angle_data:
                angle_data[angle_name] = []
            
            angle_data[angle_name].append(angle_value)
    
    # Calculate statistics
    angle_stats = []
    
    for angle_name, angles in angle_data.items():
        # Convert to numpy array
        angles = np.array(angles)
        
        # Calculate statistics
        avg_angle = np.mean(angles)
        min_angle = np.min(angles)
        max_angle = np.max(angles)
        std_deviation = np.std(angles)
        
        # Create angle stat
        angle_stat = {
            'job_id': job_id,
            'body_part': angle_name,
            'avg_angle': float(avg_angle),
            'min_angle': float(min_angle),
            'max_angle': float(max_angle),
            'std_deviation': float(std_deviation),
            'total_measurements': len(angles)
        }
        
        angle_stats.append(angle_stat)
    
    return angle_stats


def calculate_overall_reba_scores(pose_events: List[Dict[str, Any]], job_id: uuid.UUID) -> Dict[str, Any]:
    """
    Calculate overall REBA scores from pose events.
    
    Args:
        pose_events: List of pose duration events
        job_id: Job ID
    
    Returns:
        Dictionary of overall REBA scores
    """
    # Initialize score data
    score_data = {
        'job_id': job_id,
        'neck_base_score': 1,
        'neck_twist_modifier': 0,
        'neck_side_bending_modifier': 0,
        'neck_final_score': 1,
        'neck_analysis': {'pose_breakdown': {}},
        
        'trunk_base_score': 1,
        'trunk_twist_modifier': 0,
        'trunk_side_bending_modifier': 0,
        'trunk_final_score': 1,
        'trunk_analysis': {'pose_breakdown': {}},
        
        'legs_score': 1,
        'legs_analysis': {'pose_breakdown': {}},
        
        'posture_score_a': 1,
        
        'upper_arm_base_score': 1,
        'upper_arm_shoulder_modifier': 0,
        'upper_arm_abduction_modifier': 0,
        'upper_arm_support_modifier': 0,
        'upper_arm_final_score': 1,
        'upper_arm_analysis': {'pose_breakdown': {}},
        
        'lower_arm_score': 1,
        'lower_arm_analysis': {'pose_breakdown': {}},
        
        'wrist_base_score': 1,
        'wrist_deviation_modifier': 0,
        'wrist_final_score': 1,
        'wrist_analysis': {'pose_breakdown': {}},
        
        'posture_score_b': 1,
        
        'force_load_score': 0,
        'coupling_score': 0,
        'activity_score': 0,
        
        'final_reba_score': 1,
        'risk_level': 'negligible'
    }
    
    # Calculate total duration
    total_duration = sum(event['duration_seconds'] for event in pose_events)
    
    if total_duration == 0:
        return score_data
    
    # Process pose events
    body_part_scores = {}
    
    for event in pose_events:
        body_part = event['body_part']
        category = event['pose_category']
        duration = event['duration_seconds']
        score = event['final_score']
        
        # Initialize body part data
        if body_part not in body_part_scores:
            body_part_scores[body_part] = {
                'total_duration': 0,
                'weighted_score': 0,
                'categories': {}
            }
        
        # Update body part data
        body_part_scores[body_part]['total_duration'] += duration
        body_part_scores[body_part]['weighted_score'] += duration * score
        
        # Update category data
        if category not in body_part_scores[body_part]['categories']:
            body_part_scores[body_part]['categories'][category] = {
                'duration_seconds': 0,
                'percentage': 0,
                'base_score': event['base_reba_score']
            }
        
        body_part_scores[body_part]['categories'][category]['duration_seconds'] += duration
    
    # Calculate percentages and average scores
    for body_part, data in body_part_scores.items():
        # Calculate average score
        avg_score = data['weighted_score'] / data['total_duration'] if data['total_duration'] > 0 else 0
        
        # Calculate percentages
        for category, cat_data in data['categories'].items():
            cat_data['percentage'] = (cat_data['duration_seconds'] / total_duration) * 100
        
        # Update score data
        if body_part == 'neck':
            score_data['neck_base_score'] = round(avg_score)
            score_data['neck_final_score'] = round(avg_score)
            score_data['neck_analysis']['pose_breakdown'] = data['categories']
        
        elif body_part == 'trunk':
            score_data['trunk_base_score'] = round(avg_score)
            score_data['trunk_final_score'] = round(avg_score)
            score_data['trunk_analysis']['pose_breakdown'] = data['categories']
        
        elif body_part == 'legs':
            score_data['legs_score'] = round(avg_score)
            score_data['legs_analysis']['pose_breakdown'] = data['categories']
        
        elif body_part == 'upper_arm':
            score_data['upper_arm_base_score'] = round(avg_score)
            score_data['upper_arm_final_score'] = round(avg_score)
            score_data['upper_arm_analysis']['pose_breakdown'] = data['categories']
        
        elif body_part == 'lower_arm':
            score_data['lower_arm_score'] = round(avg_score)
            score_data['lower_arm_analysis']['pose_breakdown'] = data['categories']
        
        elif body_part == 'wrist':
            score_data['wrist_base_score'] = round(avg_score)
            score_data['wrist_final_score'] = round(avg_score)
            score_data['wrist_analysis']['pose_breakdown'] = data['categories']
    
    # Calculate posture scores
    score_data['posture_score_a'] = lookup_table_a(
        score_data['neck_final_score'],
        score_data['trunk_final_score'],
        score_data['legs_score']
    )
    
    score_data['posture_score_b'] = lookup_table_b(
        score_data['upper_arm_final_score'],
        score_data['lower_arm_score'],
        score_data['wrist_final_score']
    )
    
    # Calculate final REBA score
    score_a = score_data['posture_score_a'] + score_data['force_load_score']
    score_b = score_data['posture_score_b'] + score_data['coupling_score']
    
    score_c = lookup_table_c(score_a, score_b)
    
    score_data['final_reba_score'] = score_c + score_data['activity_score']
    score_data['risk_level'] = get_risk_level(score_data['final_reba_score'])
    
    return score_data
