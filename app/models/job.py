from sqlalchemy import Column, String, Float, Integer, TIMESTAMP, ForeignKey, func, Boolean
from sqlalchemy.dialects.postgresql import UUID, JSONB
import uuid
from app.db.database import Base


class Job(Base):
    __tablename__ = "jobs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    status = Column(String(20), nullable=False, default="queued")
    analysis_type = Column(String(10), nullable=False, default="reba")
    created_at = Column(TIMESTAMP, server_default=func.now())
    started_at = Column(TIMESTAMP, nullable=True)
    completed_at = Column(TIMESTAMP, nullable=True)
    file_path = Column(String(500))
    original_filename = Column(String(255))
    file_size_bytes = Column(Integer)
    duration_seconds = Column(Float)
    fps = Column(Float)
    error_message = Column(String)
    processing_options = Column(JSONB)
    job_metadata = Column(JSONB)
    
    def __repr__(self):
        return f"<Job(id={self.id}, status={self.status})>"
