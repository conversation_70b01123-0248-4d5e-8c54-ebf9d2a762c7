"""Initial REBA tables

Revision ID: 961192c5382a
Revises: 1a2b3c4d5e6f
Create Date: 2025-05-28 19:12:51.050767

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '961192c5382a'
down_revision = '1a2b3c4d5e6f'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.create_table('jobs',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('status', sa.String(length=20), nullable=False),
    sa.Column('analysis_type', sa.String(length=10), nullable=False),
    sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('now()'), nullable=True),
    sa.Column('started_at', sa.TIMESTAMP(), nullable=True),
    sa.Column('completed_at', sa.TIMESTAMP(), nullable=True),
    sa.Column('file_path', sa.String(length=500), nullable=True),
    sa.Column('original_filename', sa.String(length=255), nullable=True),
    sa.Column('file_size_bytes', sa.Integer(), nullable=True),
    sa.Column('duration_seconds', sa.Float(), nullable=True),
    sa.Column('fps', sa.Float(), nullable=True),
    sa.Column('error_message', sa.String(), nullable=True),
    sa.Column('processing_options', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('job_metadata', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('angle_summary_stats',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('job_id', sa.UUID(), nullable=True),
    sa.Column('body_part', sa.String(length=30), nullable=False),
    sa.Column('avg_angle', sa.Float(), nullable=True),
    sa.Column('min_angle', sa.Float(), nullable=True),
    sa.Column('max_angle', sa.Float(), nullable=True),
    sa.Column('std_deviation', sa.Float(), nullable=True),
    sa.Column('total_measurements', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['job_id'], ['jobs.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('frame_measurements',
    sa.Column('id', sa.BigInteger(), nullable=False),
    sa.Column('job_id', sa.UUID(), nullable=True),
    sa.Column('frame_number', sa.Integer(), nullable=False),
    sa.Column('timestamp_seconds', sa.Float(), nullable=False),
    sa.Column('reba_components', postgresql.JSONB(astext_type=sa.Text()), nullable=False),
    sa.Column('final_reba_score', sa.Integer(), nullable=True),
    sa.Column('pose_detected', sa.Boolean(), nullable=True),
    sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['job_id'], ['jobs.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('pose_duration_events',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('job_id', sa.UUID(), nullable=True),
    sa.Column('body_part', sa.String(length=30), nullable=False),
    sa.Column('pose_category', sa.String(length=50), nullable=False),
    sa.Column('start_time_seconds', sa.Float(), nullable=False),
    sa.Column('end_time_seconds', sa.Float(), nullable=False),
    sa.Column('duration_seconds', sa.Float(), nullable=False),
    sa.Column('angle_range', sa.String(length=30), nullable=True),
    sa.Column('base_reba_score', sa.Integer(), nullable=True),
    sa.Column('modifiers', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('final_score', sa.Integer(), nullable=True),
    sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['job_id'], ['jobs.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('reba_detailed_scores',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('job_id', sa.UUID(), nullable=True),
    sa.Column('neck_base_score', sa.Integer(), nullable=False),
    sa.Column('neck_twist_modifier', sa.Integer(), nullable=True),
    sa.Column('neck_side_bending_modifier', sa.Integer(), nullable=True),
    sa.Column('neck_final_score', sa.Integer(), nullable=False),
    sa.Column('neck_analysis', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('trunk_base_score', sa.Integer(), nullable=False),
    sa.Column('trunk_twist_modifier', sa.Integer(), nullable=True),
    sa.Column('trunk_side_bending_modifier', sa.Integer(), nullable=True),
    sa.Column('trunk_final_score', sa.Integer(), nullable=False),
    sa.Column('trunk_analysis', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('legs_score', sa.Integer(), nullable=False),
    sa.Column('legs_analysis', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('posture_score_a', sa.Integer(), nullable=False),
    sa.Column('upper_arm_base_score', sa.Integer(), nullable=False),
    sa.Column('upper_arm_shoulder_modifier', sa.Integer(), nullable=True),
    sa.Column('upper_arm_abduction_modifier', sa.Integer(), nullable=True),
    sa.Column('upper_arm_support_modifier', sa.Integer(), nullable=True),
    sa.Column('upper_arm_final_score', sa.Integer(), nullable=False),
    sa.Column('upper_arm_analysis', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('lower_arm_score', sa.Integer(), nullable=False),
    sa.Column('lower_arm_analysis', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('wrist_base_score', sa.Integer(), nullable=False),
    sa.Column('wrist_deviation_modifier', sa.Integer(), nullable=True),
    sa.Column('wrist_final_score', sa.Integer(), nullable=False),
    sa.Column('wrist_analysis', postgresql.JSONB(astext_type=sa.Text()), nullable=True),
    sa.Column('posture_score_b', sa.Integer(), nullable=False),
    sa.Column('force_load_score', sa.Integer(), nullable=True),
    sa.Column('coupling_score', sa.Integer(), nullable=True),
    sa.Column('activity_score', sa.Integer(), nullable=True),
    sa.Column('final_reba_score', sa.Integer(), nullable=False),
    sa.Column('risk_level', sa.String(length=20), nullable=False),
    sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['job_id'], ['jobs.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    op.create_table('reba_results',
    sa.Column('id', sa.UUID(), nullable=False),
    sa.Column('job_id', sa.UUID(), nullable=True),
    sa.Column('overall_risk_level', sa.String(length=10), nullable=True),
    sa.Column('total_frames', sa.Integer(), nullable=True),
    sa.Column('poses_detected', sa.Integer(), nullable=True),
    sa.Column('detection_rate', sa.Float(), nullable=True),
    sa.Column('processing_time_seconds', sa.Float(), nullable=True),
    sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('now()'), nullable=True),
    sa.ForeignKeyConstraint(['job_id'], ['jobs.id'], ondelete='CASCADE'),
    sa.PrimaryKeyConstraint('id')
    )
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_table('reba_results')
    op.drop_table('reba_detailed_scores')
    op.drop_table('pose_duration_events')
    op.drop_table('frame_measurements')
    op.drop_table('angle_summary_stats')
    op.drop_table('jobs')
    # ### end Alembic commands ###
