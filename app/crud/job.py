from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func
from typing import List, Optional, Dict, Any
import uuid
from datetime import datetime, timedelta

from app.models.job import Job
from app.schemas.job import JobCreate


async def create_job(db: AsyncSession, job_data: Dict[str, Any]) -> Job:
    """Create a new job in the database."""
    job = Job(**job_data)
    db.add(job)
    await db.commit()
    await db.refresh(job)
    return job


async def get_job(db: AsyncSession, job_id: uuid.UUID) -> Optional[Job]:
    """Get a job by ID."""
    result = await db.execute(select(Job).where(Job.id == job_id))
    return result.scalars().first()


async def get_jobs(
    db: AsyncSession, 
    skip: int = 0, 
    limit: int = 100, 
    status: Optional[str] = None
) -> List[Job]:
    """Get a list of jobs with optional filtering."""
    query = select(Job).order_by(Job.created_at.desc()).offset(skip).limit(limit)
    
    if status:
        query = query.where(Job.status == status)
    
    result = await db.execute(query)
    return result.scalars().all()


async def count_jobs(db: AsyncSession, status: Optional[str] = None) -> int:
    """Count the total number of jobs with optional filtering."""
    query = select(func.count(Job.id))
    
    if status:
        query = query.where(Job.status == status)
    
    result = await db.execute(query)
    return result.scalar()


async def update_job(
    db: AsyncSession, 
    job_id: uuid.UUID, 
    job_data: Dict[str, Any]
) -> Optional[Job]:
    """Update a job with the provided data."""
    await db.execute(
        update(Job)
        .where(Job.id == job_id)
        .values(**job_data)
    )
    await db.commit()
    return await get_job(db, job_id)


async def update_job_status(
    db: AsyncSession, 
    job_id: uuid.UUID, 
    status: str, 
    **kwargs
) -> Optional[Job]:
    """Update a job's status and other fields."""
    update_data = {"status": status, **kwargs}
    
    # Add timestamps based on status
    if status == "processing" and "started_at" not in kwargs:
        update_data["started_at"] = datetime.utcnow()
    elif status in ["completed", "failed"] and "completed_at" not in kwargs:
        update_data["completed_at"] = datetime.utcnow()
    
    return await update_job(db, job_id, update_data)


async def delete_job(db: AsyncSession, job_id: uuid.UUID) -> bool:
    """Delete a job by ID."""
    result = await db.execute(
        delete(Job)
        .where(Job.id == job_id)
    )
    await db.commit()
    return result.rowcount > 0


async def cleanup_old_jobs(db: AsyncSession, days: int = 30) -> int:
    """Delete jobs older than the specified number of days."""
    cutoff_date = datetime.utcnow() - timedelta(days=days)
    result = await db.execute(
        delete(Job)
        .where(Job.created_at < cutoff_date)
    )
    await db.commit()
    return result.rowcount
