-- Database initialization script for REBA Video Analysis API
-- This script is automatically executed when PostgreSQL container starts

-- Create database if it doesn't exist (this is handled by POSTGRES_DB env var)
-- CREATE DATABASE IF NOT EXISTS reba_db;

-- Create user if it doesn't exist (this is handled by POSTGRES_USER env var)
-- CREATE USER IF NOT EXISTS reba_user WITH PASSWORD 'reba_password';

-- Grant privileges
GRANT ALL PRIVILEGES ON DATABASE reba_db TO reba_user;

-- Connect to the database
\c reba_db;

-- Grant schema privileges
GRANT ALL ON SCHEMA public TO reba_user;
GRANT ALL PRIVILEGES ON ALL TABLES IN SCHEMA public TO reba_user;
GRANT ALL PRIVILEGES ON ALL SEQUENCES IN SCHEMA public TO reba_user;

-- Create extensions if needed
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Set default privileges for future tables
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON TABLES TO reba_user;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT ALL ON SEQUENCES TO reba_user;
