from fastapi import APIRouter, Depends, HTTPException, status, Query
from fastapi.responses import JSONResponse, StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any
import uuid
import pandas as pd
import io
import json

from app.db.database import get_db
from app.crud import job as job_crud
from app.crud import reba as reba_crud

router = APIRouter()


@router.get("/{job_id}/summary")
async def get_analysis_summary(
    job_id: uuid.UUID,
    db: AsyncSession = Depends(get_db)
):
    """
    Get a summary of the REBA analysis for a job.
    """
    # Check if job exists and is completed
    db_job = await job_crud.get_job(db, job_id)
    if not db_job:
        raise HTTPException(status_code=404, detail="Job not found")
    
    if db_job.status != "completed":
        raise HTTPException(
            status_code=400, 
            detail=f"Analysis not available. Current status: {db_job.status}"
        )
    
    # Get REBA analysis
    reba_result = await reba_crud.get_reba_result(db, job_id)
    reba_detailed = await reba_crud.get_reba_detailed_score(db, job_id)
    
    if not reba_result or not reba_detailed:
        raise HTTPException(status_code=404, detail="Analysis results not found")
    
    # Get risk statistics
    risk_stats = await reba_crud.get_risk_statistics(db, job_id)
    
    # Format risk statistics
    risk_distribution = {}
    for score, duration, count in risk_stats:
        risk_level = get_risk_level(score)
        if risk_level not in risk_distribution:
            risk_distribution[risk_level] = {
                "total_duration_seconds": 0,
                "percentage": 0,
                "event_count": 0
            }
        
        risk_distribution[risk_level]["total_duration_seconds"] += duration
        risk_distribution[risk_level]["event_count"] += count
    
    # Calculate percentages
    total_duration = db_job.duration_seconds or sum(item["total_duration_seconds"] for item in risk_distribution.values())
    if total_duration > 0:
        for level in risk_distribution:
            risk_distribution[level]["percentage"] = (risk_distribution[level]["total_duration_seconds"] / total_duration) * 100
    
    # Build the response
    response = {
        "job_id": job_id,
        "overall_risk_level": reba_result.overall_risk_level,
        "final_reba_score": reba_detailed.final_reba_score,
        "risk_description": get_risk_description(reba_detailed.final_reba_score),
        "analysis_stats": {
            "total_duration_seconds": db_job.duration_seconds,
            "frames_analyzed": reba_result.total_frames,
            "poses_detected": reba_result.poses_detected,
            "detection_rate": reba_result.detection_rate,
            "processing_time_seconds": reba_result.processing_time_seconds
        },
        "risk_distribution": risk_distribution,
        "highest_risk_body_parts": await get_highest_risk_body_parts(db, job_id)
    }
    
    return response


@router.get("/{job_id}/timeline")
async def get_analysis_timeline(
    job_id: uuid.UUID,
    body_part: Optional[str] = None,
    min_score: Optional[int] = None,
    db: AsyncSession = Depends(get_db)
):
    """
    Get timeline data for the REBA analysis.
    """
    # Check if job exists and is completed
    db_job = await job_crud.get_job(db, job_id)
    if not db_job:
        raise HTTPException(status_code=404, detail="Job not found")
    
    if db_job.status != "completed":
        raise HTTPException(
            status_code=400, 
            detail=f"Analysis not available. Current status: {db_job.status}"
        )
    
    # Get pose duration events
    pose_events = await reba_crud.get_pose_duration_events(
        db, job_id, body_part=body_part, min_score=min_score
    )
    
    # Format events for timeline
    timeline_events = []
    for event in pose_events:
        timeline_events.append({
            "start_time": event.start_time_seconds,
            "end_time": event.end_time_seconds,
            "duration": event.duration_seconds,
            "body_part": event.body_part,
            "pose_category": event.pose_category,
            "reba_score": event.final_score,
            "risk_level": get_risk_level(event.final_score),
            "angle_range": event.angle_range
        })
    
    # Group events by body part
    body_part_timelines = {}
    for event in timeline_events:
        body_part = event["body_part"]
        if body_part not in body_part_timelines:
            body_part_timelines[body_part] = []
        
        body_part_timelines[body_part].append(event)
    
    return {
        "job_id": job_id,
        "total_duration": db_job.duration_seconds,
        "events": timeline_events,
        "body_part_timelines": body_part_timelines
    }


@router.get("/{job_id}/body-parts")
async def get_body_part_analysis(
    job_id: uuid.UUID,
    body_part: Optional[str] = None,
    db: AsyncSession = Depends(get_db)
):
    """
    Get detailed analysis for specific body parts.
    """
    # Check if job exists and is completed
    db_job = await job_crud.get_job(db, job_id)
    if not db_job:
        raise HTTPException(status_code=404, detail="Job not found")
    
    if db_job.status != "completed":
        raise HTTPException(
            status_code=400, 
            detail=f"Analysis not available. Current status: {db_job.status}"
        )
    
    # Get REBA detailed score
    reba_detailed = await reba_crud.get_reba_detailed_score(db, job_id)
    if not reba_detailed:
        raise HTTPException(status_code=404, detail="Analysis results not found")
    
    # Get angle summary stats
    angle_stats = await reba_crud.get_angle_summary_stats(db, job_id, body_part=body_part)
    
    # Format angle stats
    angle_data = {}
    for stat in angle_stats:
        angle_data[stat.body_part] = {
            "avg_angle": stat.avg_angle,
            "min_angle": stat.min_angle,
            "max_angle": stat.max_angle,
            "std_deviation": stat.std_deviation,
            "total_measurements": stat.total_measurements
        }
    
    # Build body part analysis
    body_parts_analysis = {}
    
    # Add neck analysis
    body_parts_analysis["neck"] = {
        "base_score": reba_detailed.neck_base_score,
        "modifiers": {
            "twisted": reba_detailed.neck_twist_modifier,
            "side_bending": reba_detailed.neck_side_bending_modifier
        },
        "final_score": reba_detailed.neck_final_score,
        "pose_breakdown": reba_detailed.neck_analysis.get("pose_breakdown", {}),
        "angle_stats": angle_data.get("neck", {})
    }
    
    # Add trunk analysis
    body_parts_analysis["trunk"] = {
        "base_score": reba_detailed.trunk_base_score,
        "modifiers": {
            "twisted": reba_detailed.trunk_twist_modifier,
            "side_bending": reba_detailed.trunk_side_bending_modifier
        },
        "final_score": reba_detailed.trunk_final_score,
        "pose_breakdown": reba_detailed.trunk_analysis.get("pose_breakdown", {}),
        "angle_stats": angle_data.get("trunk", {})
    }
    
    # Add legs analysis
    body_parts_analysis["legs"] = {
        "score": reba_detailed.legs_score,
        "pose_breakdown": reba_detailed.legs_analysis.get("pose_breakdown", {}),
        "angle_stats": angle_data.get("legs", {})
    }
    
    # Add upper arm analysis
    body_parts_analysis["upper_arm"] = {
        "base_score": reba_detailed.upper_arm_base_score,
        "modifiers": {
            "shoulder_raised": reba_detailed.upper_arm_shoulder_modifier,
            "abducted": reba_detailed.upper_arm_abduction_modifier,
            "supported": reba_detailed.upper_arm_support_modifier
        },
        "final_score": reba_detailed.upper_arm_final_score,
        "pose_breakdown": reba_detailed.upper_arm_analysis.get("pose_breakdown", {}),
        "angle_stats": angle_data.get("upper_arm", {})
    }
    
    # Add lower arm analysis
    body_parts_analysis["lower_arm"] = {
        "score": reba_detailed.lower_arm_score,
        "pose_breakdown": reba_detailed.lower_arm_analysis.get("pose_breakdown", {}),
        "angle_stats": angle_data.get("lower_arm", {})
    }
    
    # Add wrist analysis
    body_parts_analysis["wrist"] = {
        "base_score": reba_detailed.wrist_base_score,
        "modifiers": {
            "bent_from_midline": reba_detailed.wrist_deviation_modifier
        },
        "final_score": reba_detailed.wrist_final_score,
        "pose_breakdown": reba_detailed.wrist_analysis.get("pose_breakdown", {}),
        "angle_stats": angle_data.get("wrist", {})
    }
    
    # Filter by body part if specified
    if body_part and body_part in body_parts_analysis:
        return {
            "job_id": job_id,
            "body_part": body_part,
            "analysis": body_parts_analysis[body_part]
        }
    
    return {
        "job_id": job_id,
        "body_parts": body_parts_analysis
    }


@router.get("/{job_id}/export")
async def export_analysis_data(
    job_id: uuid.UUID,
    format: str = Query("csv", regex="^(csv|json)$"),
    data_type: str = Query("events", regex="^(events|summary|angles)$"),
    db: AsyncSession = Depends(get_db)
):
    """
    Export analysis data in CSV or JSON format.
    """
    # Check if job exists and is completed
    db_job = await job_crud.get_job(db, job_id)
    if not db_job:
        raise HTTPException(status_code=404, detail="Job not found")
    
    if db_job.status != "completed":
        raise HTTPException(
            status_code=400, 
            detail=f"Analysis not available. Current status: {db_job.status}"
        )
    
    # Get data based on data_type
    if data_type == "events":
        # Get pose duration events
        pose_events = await reba_crud.get_pose_duration_events(db, job_id)
        
        # Convert to DataFrame
        data = []
        for event in pose_events:
            data.append({
                "start_time": event.start_time_seconds,
                "end_time": event.end_time_seconds,
                "duration": event.duration_seconds,
                "body_part": event.body_part,
                "pose_category": event.pose_category,
                "base_score": event.base_reba_score,
                "modifiers": json.dumps(event.modifiers),
                "final_score": event.final_score,
                "risk_level": get_risk_level(event.final_score)
            })
        
        df = pd.DataFrame(data)
        filename = f"reba_events_{job_id}.{format}"
    
    elif data_type == "summary":
        # Get REBA detailed score
        reba_detailed = await reba_crud.get_reba_detailed_score(db, job_id)
        if not reba_detailed:
            raise HTTPException(status_code=404, detail="Analysis results not found")
        
        # Convert to DataFrame
        data = [{
            "body_part": "neck",
            "base_score": reba_detailed.neck_base_score,
            "modifiers": json.dumps({
                "twisted": reba_detailed.neck_twist_modifier,
                "side_bending": reba_detailed.neck_side_bending_modifier
            }),
            "final_score": reba_detailed.neck_final_score
        }, {
            "body_part": "trunk",
            "base_score": reba_detailed.trunk_base_score,
            "modifiers": json.dumps({
                "twisted": reba_detailed.trunk_twist_modifier,
                "side_bending": reba_detailed.trunk_side_bending_modifier
            }),
            "final_score": reba_detailed.trunk_final_score
        }, {
            "body_part": "legs",
            "base_score": reba_detailed.legs_score,
            "modifiers": "{}",
            "final_score": reba_detailed.legs_score
        }, {
            "body_part": "upper_arm",
            "base_score": reba_detailed.upper_arm_base_score,
            "modifiers": json.dumps({
                "shoulder_raised": reba_detailed.upper_arm_shoulder_modifier,
                "abducted": reba_detailed.upper_arm_abduction_modifier,
                "supported": reba_detailed.upper_arm_support_modifier
            }),
            "final_score": reba_detailed.upper_arm_final_score
        }, {
            "body_part": "lower_arm",
            "base_score": reba_detailed.lower_arm_score,
            "modifiers": "{}",
            "final_score": reba_detailed.lower_arm_score
        }, {
            "body_part": "wrist",
            "base_score": reba_detailed.wrist_base_score,
            "modifiers": json.dumps({
                "bent_from_midline": reba_detailed.wrist_deviation_modifier
            }),
            "final_score": reba_detailed.wrist_final_score
        }]
        
        df = pd.DataFrame(data)
        filename = f"reba_summary_{job_id}.{format}"
    
    elif data_type == "angles":
        # Get angle summary stats
        angle_stats = await reba_crud.get_angle_summary_stats(db, job_id)
        
        # Convert to DataFrame
        data = []
        for stat in angle_stats:
            data.append({
                "body_part": stat.body_part,
                "avg_angle": stat.avg_angle,
                "min_angle": stat.min_angle,
                "max_angle": stat.max_angle,
                "std_deviation": stat.std_deviation,
                "total_measurements": stat.total_measurements
            })
        
        df = pd.DataFrame(data)
        filename = f"reba_angles_{job_id}.{format}"
    
    # Export data in the requested format
    if format == "csv":
        csv_data = df.to_csv(index=False)
        return StreamingResponse(
            io.StringIO(csv_data),
            media_type="text/csv",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    else:  # format == "json"
        json_data = df.to_json(orient="records")
        return JSONResponse(
            content=json.loads(json_data),
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )


async def get_highest_risk_body_parts(db: AsyncSession, job_id: uuid.UUID) -> List[Dict[str, Any]]:
    """
    Get the body parts with the highest risk scores.
    """
    # Get pose duration events
    pose_events = await reba_crud.get_pose_duration_events(db, job_id)
    
    # Calculate total duration and high-risk duration for each body part
    body_part_stats = {}
    for event in pose_events:
        body_part = event.body_part
        if body_part not in body_part_stats:
            body_part_stats[body_part] = {
                "total_duration": 0,
                "high_risk_duration": 0,
                "max_score": 0,
                "avg_score": 0,
                "score_weighted_duration": 0
            }
        
        body_part_stats[body_part]["total_duration"] += event.duration_seconds
        body_part_stats[body_part]["score_weighted_duration"] += event.duration_seconds * event.final_score
        
        if event.final_score >= 8:  # High risk threshold
            body_part_stats[body_part]["high_risk_duration"] += event.duration_seconds
        
        if event.final_score > body_part_stats[body_part]["max_score"]:
            body_part_stats[body_part]["max_score"] = event.final_score
    
    # Calculate average score
    for body_part in body_part_stats:
        if body_part_stats[body_part]["total_duration"] > 0:
            body_part_stats[body_part]["avg_score"] = (
                body_part_stats[body_part]["score_weighted_duration"] / 
                body_part_stats[body_part]["total_duration"]
            )
    
    # Sort by high-risk duration percentage
    result = []
    for body_part, stats in body_part_stats.items():
        if stats["total_duration"] > 0:
            result.append({
                "body_part": body_part,
                "max_score": stats["max_score"],
                "avg_score": round(stats["avg_score"], 2),
                "high_risk_percentage": (stats["high_risk_duration"] / stats["total_duration"]) * 100,
                "total_duration": stats["total_duration"]
            })
    
    # Sort by high-risk percentage (descending)
    result.sort(key=lambda x: x["high_risk_percentage"], reverse=True)
    
    return result[:3]  # Return top 3 highest risk body parts


def get_risk_level(score: int) -> str:
    """
    Get the risk level based on the REBA score.
    """
    if score == 1:
        return "negligible"
    elif score in [2, 3]:
        return "low"
    elif score in [4, 5, 6, 7]:
        return "medium"
    elif score in [8, 9, 10]:
        return "high"
    else:
        return "very_high"


def get_risk_description(score: int) -> str:
    """
    Get the risk description based on the REBA score.
    """
    if score == 1:
        return "Negligible risk"
    elif score in [2, 3]:
        return "Low risk, change may be needed"
    elif score in [4, 5, 6, 7]:
        return "Medium risk, further investigate, change soon"
    elif score in [8, 9, 10]:
        return "High risk, investigate and implement change"
    else:
        return "Very high risk, implement change immediately"
