from pydantic import BaseModel, UUID4, Field
from typing import Optional, Dict, Any, List
from datetime import datetime
import uuid


class PoseBreakdown(BaseModel):
    duration_seconds: float
    percentage: float
    base_score: int


class ComponentModifiers(BaseModel):
    twisted: Optional[int] = 0
    side_bending: Optional[int] = 0
    shoulder_raised: Optional[int] = 0
    abducted: Optional[int] = 0
    supported: Optional[int] = 0
    bent_from_midline: Optional[int] = 0


class ComponentAnalysis(BaseModel):
    base_score: int
    modifiers: Dict[str, int]
    final_score: int
    pose_breakdown: Dict[str, PoseBreakdown]


class GroupA(BaseModel):
    neck: ComponentAnalysis
    trunk: ComponentAnalysis
    legs: Dict[str, Any]
    posture_score_a: int


class GroupB(BaseModel):
    upper_arm: ComponentAnalysis
    lower_arm: Dict[str, Any]
    wrist: ComponentAnalysis
    posture_score_b: int


class REBAModifiers(BaseModel):
    force_load_score: int
    coupling_score: int
    activity_score: int


class REBAAnalysisSummary(BaseModel):
    overall_risk_level: str
    total_duration_seconds: float
    frames_analyzed: int
    poses_detected: int
    detection_rate: float


class REBAComponentBreakdown(BaseModel):
    group_a: GroupA
    group_b: GroupB
    modifiers: REBAModifiers


class RiskEvent(BaseModel):
    timestamp: float
    duration: float
    body_parts: List[str]
    max_score: int
    description: str


class AssetUrls(BaseModel):
    annotated_video_url: Optional[str] = None
    timeline_chart_url: Optional[str] = None
    csv_export_url: Optional[str] = None


class REBAAnalysisResult(BaseModel):
    job_id: UUID4
    analysis_summary: REBAAnalysisSummary
    reba_analysis: Dict[str, Any]
    risk_events: List[RiskEvent]
    assets: Optional[AssetUrls] = None


class PoseDurationEventCreate(BaseModel):
    job_id: UUID4
    body_part: str
    pose_category: str
    start_time_seconds: float
    end_time_seconds: float
    duration_seconds: float
    angle_range: Optional[str] = None
    base_reba_score: int
    modifiers: Optional[Dict[str, int]] = None
    final_score: int


class PoseDurationEventResponse(PoseDurationEventCreate):
    id: UUID4
    created_at: datetime
    
    class Config:
        from_attributes = True


class AngleSummaryStatCreate(BaseModel):
    job_id: UUID4
    body_part: str
    avg_angle: float
    min_angle: float
    max_angle: float
    std_deviation: float
    total_measurements: int


class AngleSummaryStatResponse(AngleSummaryStatCreate):
    id: UUID4
    created_at: datetime
    
    class Config:
        from_attributes = True


class REBADetailedScoreCreate(BaseModel):
    job_id: UUID4
    neck_base_score: int
    neck_twist_modifier: int = 0
    neck_side_bending_modifier: int = 0
    neck_final_score: int
    neck_analysis: Dict[str, Any]
    
    trunk_base_score: int
    trunk_twist_modifier: int = 0
    trunk_side_bending_modifier: int = 0
    trunk_final_score: int
    trunk_analysis: Dict[str, Any]
    
    legs_score: int
    legs_analysis: Dict[str, Any]
    
    posture_score_a: int
    
    upper_arm_base_score: int
    upper_arm_shoulder_modifier: int = 0
    upper_arm_abduction_modifier: int = 0
    upper_arm_support_modifier: int = 0
    upper_arm_final_score: int
    upper_arm_analysis: Dict[str, Any]
    
    lower_arm_score: int
    lower_arm_analysis: Dict[str, Any]
    
    wrist_base_score: int
    wrist_deviation_modifier: int = 0
    wrist_final_score: int
    wrist_analysis: Dict[str, Any]
    
    posture_score_b: int
    
    force_load_score: int = 0
    coupling_score: int = 0
    activity_score: int = 0
    
    final_reba_score: int
    risk_level: str


class REBADetailedScoreResponse(REBADetailedScoreCreate):
    id: UUID4
    created_at: datetime
    
    class Config:
        from_attributes = True
