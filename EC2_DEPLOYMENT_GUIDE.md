# EC2 Deployment Guide - REBA Video Analysis API

## Prerequisites

### EC2 Instance Requirements
- **Instance Type**: t3.medium or larger (minimum 2 vCPU, 4GB RAM)
- **Storage**: 20GB+ EBS volume
- **OS**: Ubuntu 22.04 LTS
- **Security Group**: Allow ports 22 (SSH), 80 (HTTP), 443 (HTTPS), 9000 (API)

### Local Requirements
- Git
- SSH key for EC2 access

## Step 1: Launch EC2 Instance

1. **Launch Ubuntu 22.04 LTS instance**
2. **Configure Security Group**:
   ```
   SSH (22): Your IP
   HTTP (80): 0.0.0.0/0
   HTTPS (443): 0.0.0.0/0
   Custom TCP (9000): 0.0.0.0/0  # For API access
   ```
3. **Create or use existing key pair**
4. **Launch instance**

## Step 2: Connect to EC2 Instance

```bash
ssh -i your-key.pem ubuntu@your-ec2-public-ip
```

## Step 3: Install Dependencies on EC2

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker ubuntu

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Install Git
sudo apt install git -y

# Logout and login again to apply docker group changes
exit
```

## Step 4: Clone and Setup Application

```bash
# SSH back into the instance
ssh -i your-key.pem ubuntu@your-ec2-public-ip

# Clone your repository
git clone https://github.com/your-username/ai-backend-industrial.git
cd ai-backend-industrial

# Copy production environment file
cp .env.production .env

# Edit environment file with your settings
nano .env
```

### Important Environment Variables to Update:

```bash
# Generate a secure secret key
SECRET_KEY=your-super-secure-secret-key-here

# Update database password
DATABASE_URL=postgresql+asyncpg://reba_user:YOUR_SECURE_PASSWORD@postgres:5432/reba_db?ssl=disable
ASYNC_DATABASE_URL=postgresql+asyncpg://reba_user:YOUR_SECURE_PASSWORD@postgres:5432/reba_db?ssl=disable

# Set production mode
DEBUG=False

# Update allowed hosts (replace with your domain)
ALLOWED_HOSTS=["your-domain.com", "your-ec2-public-ip"]
CORS_ORIGINS=["https://your-frontend-domain.com"]
```

## Step 5: Deploy Application

```bash
# Make deployment script executable
chmod +x deploy.sh

# Run deployment script
./deploy.sh

# Select option 2 for production deployment
```

## Step 6: Configure Domain (Optional)

### Using Route 53 or your DNS provider:
1. Create A record pointing to your EC2 public IP
2. Update `.env` file with your domain
3. Configure SSL certificate (see SSL section below)

## Step 7: SSL Configuration (Production)

### Option 1: Let's Encrypt with Certbot

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Get SSL certificate
sudo certbot --nginx -d your-domain.com

# Update nginx configuration to use SSL
# Edit nginx.conf and uncomment SSL section
```

### Option 2: Manual SSL Certificate

```bash
# Create SSL directory
mkdir -p ssl

# Copy your certificate files
# ssl/cert.pem
# ssl/key.pem

# Update docker-compose.prod.yml to mount SSL directory
```

## Step 8: Monitoring and Maintenance

### Check Application Status
```bash
# Check all services
docker-compose -f docker-compose.prod.yml ps

# Check logs
docker-compose -f docker-compose.prod.yml logs -f

# Check health endpoint
curl http://localhost:9000/api/v1/health
```

### Backup Strategy
```bash
# Backup database
docker-compose -f docker-compose.prod.yml exec postgres pg_dump -U reba_user reba_db > backup.sql

# Backup uploads
tar -czf uploads-backup.tar.gz uploads/
```

### Updates
```bash
# Pull latest code
git pull origin main

# Rebuild and restart
./deploy.sh
```

## Step 9: Testing Deployment

### Test API Endpoints
```bash
# Health check
curl http://your-ec2-ip:9000/api/v1/health

# Upload test video
curl -X POST "http://your-ec2-ip:9000/api/v1/videos/upload" \
  -F "file=@test-video.mp4" \
  -F "analysis_type=reba" \
  -F "options={\"detection_confidence\": 0.87}"
```

### Access API Documentation
Visit: `http://your-ec2-ip:9000/api/v1/docs`

## Troubleshooting

### Common Issues

1. **Port 9000 not accessible**
   - Check EC2 Security Group
   - Verify docker containers are running

2. **Database connection errors**
   - Check PostgreSQL container logs
   - Verify environment variables

3. **Celery worker not processing jobs**
   - Check Redis connection
   - Check Celery worker logs

4. **Out of disk space**
   - Clean up old uploads: `docker system prune -a`
   - Increase EBS volume size

### Useful Commands

```bash
# View all logs
docker-compose -f docker-compose.prod.yml logs

# Restart specific service
docker-compose -f docker-compose.prod.yml restart web

# Access container shell
docker-compose -f docker-compose.prod.yml exec web bash

# Monitor system resources
htop
df -h
```

## Security Considerations

1. **Change default passwords**
2. **Use strong SECRET_KEY**
3. **Configure firewall rules**
4. **Enable SSL/TLS**
5. **Regular security updates**
6. **Monitor access logs**
7. **Backup regularly**

## Performance Optimization

1. **Instance sizing**: Scale based on usage
2. **Database optimization**: Tune PostgreSQL settings
3. **Caching**: Redis configuration
4. **File storage**: Consider S3 for uploads
5. **CDN**: CloudFront for static assets
6. **Load balancing**: Multiple instances behind ALB
