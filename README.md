# REBA Video Analysis API

A scalable FastAPI backend service that processes video uploads to perform real-time ergonomic risk assessment using REBA (Rapid Entire Body Assessment) methodology, providing actionable insights for workplace safety optimization.

## Features

- Automated Ergonomic Assessment: Transform manual REBA scoring into automated video analysis
- Real-time Risk Detection: Identify high-risk postures and movements as they occur
- Scalable Processing: Handle concurrent video uploads with queue-based processing
- Comprehensive Reporting: Generate detailed analytics with temporal data and risk trends

## Tech Stack

- **API Layer**: FastAPI
- **Processing Engine**: Redis/Celery, MediaPipe
- **Data Layer**: PostgreSQL with JSONB
- **Infrastructure**: Docker

## Setup Instructions

### Prerequisites

- Python 3.9+
- PostgreSQL
- Redis

### Installation

1. Clone the repository:
```
git clone <repository-url>
cd ai-backend-industrial
```

2. Create a virtual environment and activate it:
```
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:
```
pip install -r requirements.txt
```

4. Set up environment variables:
```
cp .env.example .env
```
Edit the `.env` file with your database credentials and other configuration.

5. Initialize the database:
```
alembic upgrade head
```

### Running the Application

1. Start the Redis server:
```
redis-server
```

2. Start Celery workers:
```
celery -A app.core.celery_app worker --loglevel=info
```

3. Run the FastAPI application:
```
uvicorn app.main:app --reload
```

4. Access the API documentation at `http://localhost:8000/docs`

## API Endpoints

### Video Upload & Job Management
- `POST /api/v1/videos/upload` - Upload a video for REBA analysis
- `GET /api/v1/jobs/{job_id}/status` - Check job status
- `GET /api/v1/jobs/{job_id}/results` - Get analysis results
- `DELETE /api/v1/jobs/{job_id}` - Delete a job
- `GET /api/v1/jobs` - List all jobs

### Analysis & Reporting
- `GET /api/v1/analysis/{job_id}/summary` - Get analysis summary
- `GET /api/v1/analysis/{job_id}/timeline` - Get timeline data
- `GET /api/v1/analysis/{job_id}/body-parts` - Get body part analysis
- `GET /api/v1/analysis/{job_id}/export` - Export data (CSV/PDF)

### System & Health
- `GET /api/v1/health` - Check API health
- `GET /api/v1/system/stats` - Get system statistics

## License

[MIT License](LICENSE)
