#!/usr/bin/env python3
"""
Test script for REBA API deployment with shared Nginx setup
"""

import requests
import json
import time
import sys
import os
from pathlib import Path

# Configuration for different access methods
CONFIGS = {
    "direct": {
        "base_url": "http://localhost:9001/api/v1",
        "name": "Direct Container Access"
    },
    "nginx_path": {
        "base_url": "http://localhost/reba/api/v1",  # Adjust if your domain is different
        "name": "Nginx Path-based Routing"
    },
    "nginx_subdomain": {
        "base_url": "http://reba.localhost/api/v1",  # Adjust to your subdomain
        "name": "Nginx Subdomain Routing"
    }
}

def find_test_video():
    """Find a test video in the uploads directory"""
    uploads_dir = Path("uploads")
    if uploads_dir.exists():
        for video_file in uploads_dir.rglob("*.mp4"):
            return str(video_file)
    return None

def test_health_check(config):
    """Test the health check endpoint"""
    print(f"🏥 Testing health check via {config['name']}...")
    try:
        response = requests.get(f"{config['base_url']}/health", timeout=10)
        if response.status_code == 200:
            health_data = response.json()
            print(f"✅ Health check passed")
            print(f"   Status: {health_data.get('status')}")
            print(f"   Database: {health_data.get('dependencies', {}).get('database')}")
            print(f"   Redis: {health_data.get('dependencies', {}).get('redis')}")
            return True
        else:
            print(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Health check error: {e}")
        return False

def test_api_docs(config):
    """Test if API documentation is accessible"""
    print(f"\n📚 Testing API documentation via {config['name']}...")
    try:
        response = requests.get(f"{config['base_url']}/docs", timeout=10)
        if response.status_code == 200:
            print("✅ API documentation accessible")
            return True
        else:
            print(f"❌ API docs failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API docs error: {e}")
        return False

def test_video_upload(config):
    """Test video upload functionality"""
    print(f"\n🎥 Testing video upload via {config['name']}...")
    
    test_video = find_test_video()
    if not test_video:
        print("⚠️  No test video found in uploads directory")
        print("   Skipping video upload test")
        return True
    
    print(f"   Using test video: {test_video}")
    
    try:
        with open(test_video, 'rb') as f:
            files = {'file': (os.path.basename(test_video), f, 'video/mp4')}
            data = {
                'analysis_type': 'reba',
                'options': json.dumps({
                    "detection_confidence": 0.87,
                    "model_complexity": 2,
                    "max_duration_seconds": 10  # Short test
                }),
                'metadata': json.dumps({"test": "shared_nginx_deployment"})
            }
            
            response = requests.post(f"{config['base_url']}/videos/upload", files=files, data=data, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                job_id = result['id']
                print(f"✅ Video uploaded successfully")
                print(f"   Job ID: {job_id}")
                print(f"   Status: {result['status']}")
                return job_id
            else:
                print(f"❌ Video upload failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return None
                
    except Exception as e:
        print(f"❌ Video upload error: {e}")
        return None

def test_configuration(config_name, config):
    """Test a specific configuration"""
    print(f"\n{'='*60}")
    print(f"Testing: {config['name']}")
    print(f"URL: {config['base_url']}")
    print(f"{'='*60}")
    
    tests_passed = 0
    total_tests = 3
    
    # Test 1: Health Check
    if test_health_check(config):
        tests_passed += 1
    
    # Test 2: API Documentation
    if test_api_docs(config):
        tests_passed += 1
    
    # Test 3: Video Upload (only for working configs)
    if tests_passed == 2:  # Only test upload if basic tests pass
        if test_video_upload(config):
            tests_passed += 1
    else:
        print("\n🎥 Skipping video upload test due to previous failures")
    
    print(f"\n📊 {config['name']}: {tests_passed}/{total_tests} tests passed")
    return tests_passed, total_tests

def main():
    """Run tests for all configurations"""
    print("🚀 REBA Video Analysis API - Shared Nginx Deployment Test")
    print("=" * 70)
    
    # Test direct container access first
    print("🔍 Testing direct container access to verify REBA API is running...")
    direct_passed, direct_total = test_configuration("direct", CONFIGS["direct"])
    
    if direct_passed < 2:
        print("\n❌ Direct container access failed. REBA API may not be running properly.")
        print("   Please check: docker-compose -f docker-compose.shared-nginx.yml ps")
        print("   And logs: docker-compose -f docker-compose.shared-nginx.yml logs reba-web")
        sys.exit(1)
    
    print("\n✅ Direct container access working. Testing Nginx configurations...")
    
    # Test Nginx configurations
    total_passed = direct_passed
    total_tests = direct_total
    
    for config_name, config in CONFIGS.items():
        if config_name == "direct":
            continue  # Already tested
            
        passed, tests = test_configuration(config_name, config)
        total_passed += passed
        total_tests += tests
    
    # Summary
    print("\n" + "=" * 70)
    print(f"📋 Overall Test Summary: {total_passed}/{total_tests} tests passed")
    
    print("\n🔗 Access URLs:")
    print(f"   Direct Container: {CONFIGS['direct']['base_url']}/docs")
    print(f"   Nginx Path-based: {CONFIGS['nginx_path']['base_url']}/docs")
    print(f"   Nginx Subdomain: {CONFIGS['nginx_subdomain']['base_url']}/docs")
    
    print("\n📝 Next Steps:")
    if total_passed >= 4:  # Direct access working
        print("   1. ✅ REBA API is running correctly")
        print("   2. 🔧 Configure your Nginx with the provided configuration")
        print("   3. 🧪 Test Nginx routing after configuration")
        print("   4. 🌐 Update DNS if using subdomain routing")
    else:
        print("   1. ❌ Fix REBA API deployment issues")
        print("   2. 🔧 Check Docker container logs")
        print("   3. 🔍 Verify environment configuration")
    
    if total_passed == total_tests:
        print("\n🎉 All tests passed! Deployment is working correctly.")
        sys.exit(0)
    else:
        print(f"\n⚠️  {total_tests - total_passed} tests failed. Please check the configuration.")
        sys.exit(1)

if __name__ == "__main__":
    main()
