from fastapi import APIRouter, Depends, HTTPException, status, UploadFile, File, Form
from fastapi.responses import JSONResponse
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Optional, Dict, Any, List
import uuid
import os
import json
import aiofiles
from datetime import datetime, timedelta
import cv2
import asyncio

from app.db.database import get_db
from app.core.config import settings
from app.schemas.job import JobResponse, ProcessingOptions, JobMetadata
from app.crud import job as job_crud
from app.services.video.tasks import process_video

router = APIRouter()


@router.post("/upload", response_model=JobResponse)
async def upload_video(
    file: UploadFile = File(...),
    analysis_type: str = Form("reba"),
    options: str = Form("{}"),
    metadata: str = Form("{}"),
    db: AsyncSession = Depends(get_db)
):
    """
    Upload a video for REBA analysis.
    """
    # Validate file
    if not file.filename:
        raise HTTPException(status_code=400, detail="File name is required")
    
    # Check file extension
    file_ext = file.filename.split(".")[-1].lower()
    if file_ext not in settings.ALLOWED_VIDEO_FORMATS:
        raise HTTPException(
            status_code=400, 
            detail=f"Unsupported file format. Allowed formats: {', '.join(settings.ALLOWED_VIDEO_FORMATS)}"
        )
    
    # Parse options and metadata
    try:
        processing_options = json.loads(options)
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid options JSON format")
    
    try:
        job_metadata = json.loads(metadata)
    except json.JSONDecodeError:
        raise HTTPException(status_code=400, detail="Invalid metadata JSON format")
    
    # Create a unique filename
    unique_id = uuid.uuid4()
    unique_filename = f"{unique_id}.{file_ext}"
    
    # Create upload directory if it doesn't exist
    upload_dir = os.path.join(settings.UPLOAD_DIR, str(unique_id))
    os.makedirs(upload_dir, exist_ok=True)
    
    # Save the file
    file_path = os.path.join(upload_dir, unique_filename)
    async with aiofiles.open(file_path, "wb") as out_file:
        # Read and write in chunks to handle large files
        while content := await file.read(1024 * 1024):  # 1MB chunks
            await out_file.write(content)
    
    # Get file size
    file_size = os.path.getsize(file_path)
    
    # Check file size
    if file_size > settings.max_file_size:
        # Clean up the file
        os.remove(file_path)
        raise HTTPException(
            status_code=400, 
            detail=f"File too large. Maximum size: {settings.max_file_size / (1024 * 1024)} MB"
        )
    
    # Get video duration and fps (this is CPU-intensive, so do it in a separate thread)
    try:
        duration, fps = await get_video_info(file_path)
        
        # Check video duration
        if duration > settings.max_video_duration:
            # Clean up the file
            os.remove(file_path)
            raise HTTPException(
                status_code=400, 
                detail=f"Video too long. Maximum duration: {settings.max_video_duration / 60} minutes"
            )
    except Exception as e:
        # Clean up the file
        os.remove(file_path)
        raise HTTPException(status_code=400, detail=f"Invalid video file: {str(e)}")
    
    # Create job in database
    job_data = {
        "status": "queued",
        "analysis_type": analysis_type,
        "file_path": file_path,
        "original_filename": file.filename,
        "file_size_bytes": file_size,
        "duration_seconds": duration,
        "fps": fps,
        "processing_options": processing_options,
        "metadata": job_metadata
    }
    
    db_job = await job_crud.create_job(db, job_data)
    
    # Queue the job for processing
    process_video.delay(str(db_job.id))
    
    # Calculate estimated completion time (rough estimate)
    estimated_completion = datetime.utcnow() + timedelta(seconds=duration * 2)  # Assume processing takes 2x real-time
    
    # Create response
    response = {
        "id": db_job.id,  # Changed from 'job_id' to 'id' to match JobResponse schema
        "status": db_job.status,
        "created_at": db_job.created_at,
        "estimated_completion": estimated_completion,
        "file_info": {
            "filename": db_job.original_filename,
            "size_bytes": db_job.file_size_bytes,
            "duration_seconds": db_job.duration_seconds,
            "fps": db_job.fps
        }
    }
    
    return response


async def get_video_info(file_path: str) -> tuple:
    """
    Get video duration and fps using OpenCV.
    This is run in a separate thread to avoid blocking the event loop.
    """
    def _get_video_info():
        cap = cv2.VideoCapture(file_path)
        if not cap.isOpened():
            raise ValueError("Could not open video file")
        
        # Get fps
        fps = cap.get(cv2.CAP_PROP_FPS)
        
        # Get frame count
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        
        # Calculate duration
        duration = frame_count / fps if fps > 0 else 0
        
        cap.release()
        return duration, fps
    
    # Run in a separate thread to avoid blocking
    return await asyncio.to_thread(_get_video_info)
