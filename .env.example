# API Settings
API_V1_STR=/api/v1
PROJECT_NAME=REBA Video Analysis API
SECRET_KEY=your-secret-key-here
ACCESS_TOKEN_EXPIRE_MINUTES=60
ALGORITHM=HS256

# Database Settings
DATABASE_URL=postgresql+asyncpg://postgres:postgres@localhost:5432/reba_db
ASYNC_DATABASE_URL=postgresql+asyncpg://postgres:postgres@localhost:5432/reba_db

# Redis Settings
REDIS_HOST=localhost
REDIS_PORT=6379

# Celery Settings
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0

# File Storage Settings
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=524288000  # 500MB in bytes
MAX_VIDEO_DURATION=1800  # 30 minutes in seconds

# Processing Settings
DETECTION_CONFIDENCE=0.87
MODEL_COMPLEXITY=2
FRAME_RATE_REDUCTION=1
