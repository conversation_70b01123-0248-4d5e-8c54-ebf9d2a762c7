import cv2
import mediapipe as mp
import argparse
import math
from collections import defaultdict
import csv
import matplotlib.pyplot as plt
import os
from matplotlib.collections import LineCollection
import numpy as np
from matplotlib.lines import Line2D


# -------------------- Argument Parsing --------------------
parser = argparse.ArgumentParser()
parser.add_argument("--input", "-i", required=True, help="Input video path")
parser.add_argument("--output", "-o", required=True, help="Output annotated video path")
args = parser.parse_args()
output_dir = os.path.splitext(args.output)[0] + "_output"
os.makedirs(output_dir, exist_ok=True)

# -------------------- Setup MediaPipe Pose --------------------
mp_pose = mp.solutions.pose
pose = mp_pose.Pose(
    static_image_mode=False,
    min_detection_confidence=0.87,
    model_complexity=2
)
mp_drawing = mp.solutions.drawing_utils
pose_connections = mp_pose.POSE_CONNECTIONS
landmarks_enum = mp_pose.PoseLandmark

# -------------------- Video Setup --------------------
cap = cv2.VideoCapture(args.input)
fps = cap.get(cv2.CAP_PROP_FPS)
width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

fourcc = cv2.VideoWriter_fourcc(*'mp4v')
out = cv2.VideoWriter(output_dir+"/"+args.output, fourcc, fps, (width, height))

# -------------------- Helpers --------------------
def to_xy(landmark):
    return int(landmark.x * width), int(landmark.y * height)

def angle_between(p1, p2, p3):
    a = math.dist(p1, p2)
    b = math.dist(p3, p2)
    c = math.dist(p1, p3)
    try:
        angle = math.acos((a**2 + b**2 - c**2) / (2 * a * b))
        return math.degrees(angle)
    except:
        return 0

def get_neck_flexion_angle(ear_avg, shoulders_center):
    dx = ear_avg[0] - shoulders_center[0]
    dy = shoulders_center[1] - ear_avg[1]
    angle_rad = math.atan2(dx, dy)
    return (math.degrees(angle_rad))

def get_trunk_angle_yaxis(shoulder, hip):
    dx = hip[0] - shoulder[0]
    dy = shoulder[1] - hip[1]
    try:
        angle = math.atan2(abs(dx), abs(dy))
        return (math.degrees(angle))
    except:
        return 0
    
def get_shoulder_angle_yaxis(elbow, shoulder):
    dx = elbow[0] - shoulder[0]
    dy = shoulder[1] - elbow[1]
    try:
        angle = math.atan2(abs(dx), abs(dy))
        return abs(math.degrees(angle))
    except:
        return 0

def get_neck_twist_lateral(left_ear, right_ear):
    dx = left_ear[0] - right_ear[0]
    return abs(dx)

def get_trunk_twist_lateral(left_shoulder, right_shoulder):
    dx = left_shoulder[0] - right_shoulder[0]
    return abs(dx)

def reba_score_neck(angle):
    if angle < 20:
        return "Neck 0 points", 0
    elif angle < 30:
        return "Neck 1 point", 1
    else:
        return "Neck 2 points", 2

def reba_score_arm(angle):
    if 60 <= angle <= 100:
        return "Arm 1 point", 1
    else:
        return "Arm 2 points", 2

def reba_score_neck_twist(value):
    if value <= 5:
        return "Neck twist modifier 0 point", 0
    else:
        return "Neck twist modifier 1 point", 1
    
def reba_score_trunk_twist(value):
    if value <= 7:
        return "Trunk twist modifier 0 point", 0
    else:
        return "Trunk twist modifier 1 point", 1

def reba_score_leg(knee_angle):
    if knee_angle > 150:
        return "Leg 1 point", 1
    elif 120 <= knee_angle <= 150:
        return "Leg 2 points", 2
    else:
        return "Leg 3 points", 3

def reba_score_trunk(angle):
    if angle < 5:
        return "Trunk 1 point", 1
    elif angle < 20:
        return "Trunk 2 points", 2
    elif angle < 60:
        return "Trunk 3 points", 3
    else:
        return "Trunk 4 points", 4

def reba_score_shoulder(angle):
    if angle < 20:
        return "Shoulder 1 point", 1
    elif angle < 45:
        return "Shoulder 2 points", 2
    elif angle < 90:
        return "Shoulder 3 points", 3
    else:
        return "Shoulder 4 points", 4
    
def reba_score_wrist(angle):
    if angle < 165:
        return "Wrist 2 points", 2
    else:
        return "Wrist 1 point", 1
    
def reba_score_legdiff(angle):
    if angle < 20:
        return "Leg difference 1 point", 1
    else:
        return "Leg difference 2 points", 2

def get_color_for_score(score):
    if score == 0:
        return (0, 255, 0)     # Green
    elif score == 1:
        return (0, 255, 255)   # Yellow
    elif score == 2:
        return (0, 165, 255)   # Orange
    elif score == 3:
        return (0, 0, 255)     # Red
    elif score == 4:
        return (0, 0, 0)       # Black
    return (255, 255, 255)     # Default white

# -------------------- Angle Tracking --------------------
angles_over_time = {
    "time": [],
    "neck": [],
    "trunk": [],
    "left_arm": [],
    "right_arm": [],
    "left_shoulder": [],
    "right_shoulder": [],
    "left_wrist": [],
    "right_wrist": [],
    "left_leg": [],
    "right_leg": [],
    "leg_diff": [],
    "neck_twist": [],
    "trunk_twist": []
}

# -------------------- Frame Count Tracking --------------------
frame_counts = defaultdict(int)
frame_counts_left_arm = defaultdict(int)
frame_counts_right_arm = defaultdict(int)
frame_counts_left_shoulder = defaultdict(int)
frame_counts_right_shoulder = defaultdict(int)
frame_counts_left_leg = defaultdict(int)
frame_counts_right_leg = defaultdict(int)
frame_counts_left_wrist = defaultdict(int)
frame_counts_right_wrist = defaultdict(int)
frame_counts_diff_leg = defaultdict(int)
frame_counts_trunk_twist = defaultdict(int)
frame_counts_neck_twist = defaultdict(int)

# -------------------- Main Loop --------------------
while True:
    ret, frame = cap.read()
    if not ret:
        break

    rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
    results = pose.process(rgb)

    if results.pose_landmarks:
        lm = results.pose_landmarks.landmark
        to = lambda l: to_xy(lm[landmarks_enum[l]])

        # Get key points
        l_shoulder, r_shoulder = to("LEFT_SHOULDER"), to("RIGHT_SHOULDER")
        l_ear, r_ear = to("LEFT_EAR"), to("RIGHT_EAR")
        l_elbow, r_elbow = to("LEFT_ELBOW"), to("RIGHT_ELBOW")
        l_wrist, r_wrist = to("LEFT_WRIST"), to("RIGHT_WRIST")
        l_hip, r_hip = to("LEFT_HIP"), to("RIGHT_HIP")
        l_knee, r_knee = to("LEFT_KNEE"), to("RIGHT_KNEE")
        l_ankle, r_ankle = to("LEFT_ANKLE"), to("RIGHT_ANKLE")
        l_index, r_index = to("LEFT_INDEX"), to("RIGHT_INDEX")
        mid_shoulder = ((l_shoulder[0] + r_shoulder[0]) // 2, (l_shoulder[1] + r_shoulder[1]) // 2)
        mid_hip = ((l_hip[0] + r_hip[0]) // 2, (l_hip[1] + r_hip[1]) // 2)
        mid_ear = ((l_ear[0] + r_ear[0]) // 2, (l_ear[1] + r_ear[1]) // 2)
        mid_hip_vis = ((l_hip[0] + r_hip[0]) // 2 +10, (l_hip[1] + r_hip[1]) // 2 -10)
        mid_ear_vis = ((l_ear[0] + r_ear[0]) // 2 +10, (l_ear[1] + r_ear[1]) // 2 -10)

        # Neck
        neck_angle = get_neck_flexion_angle(mid_ear, mid_shoulder)
        neck_twist = get_neck_twist_lateral(l_ear,r_ear)
        neck_label, neck_score = reba_score_neck(abs(neck_angle))
        neck_twist_label, neck_twist_score = reba_score_neck_twist(neck_twist)
        frame_counts[neck_label] += 1
        frame_counts_neck_twist[neck_twist_label] += 1
        neck_color = get_color_for_score(neck_score)
        
        cv2.line(frame, mid_shoulder, mid_ear, neck_color, 2)

        # Trunk
        trunk_angle = get_trunk_angle_yaxis(mid_shoulder, mid_hip)
        trunk_twist = get_neck_twist_lateral(l_ear,r_ear)
        trunk_label, trunk_score = reba_score_trunk(abs(trunk_angle))
        trunk_twist_label, trunk_twist_score = reba_score_trunk_twist(trunk_twist)
        frame_counts[trunk_label] += 1
        frame_counts_trunk_twist[trunk_twist_label] += 1
        trunk_color = get_color_for_score(trunk_score)
        cv2.line(frame, mid_shoulder, mid_hip, trunk_color, 2)
        
        #Time
        frame_time = cap.get(cv2.CAP_PROP_POS_MSEC) / 1000.0  # Time in seconds
        angles_over_time["time"].append(frame_time)

        # Neck and Trunk
        angles_over_time["neck"].append(neck_angle)
        angles_over_time["trunk"].append(trunk_angle)
        angles_over_time["neck_twist"].append(neck_twist)
        angles_over_time["trunk_twist"].append(trunk_twist)

        # Arms
        for side in ["LEFT", "RIGHT"]:
            shoulder = to(f"{side}_SHOULDER")
            elbow = to(f"{side}_ELBOW")
            wrist = to(f"{side}_WRIST")
            index = to(f"{side}_INDEX")
            arm_angle = angle_between(shoulder, elbow, wrist)
            wrist_angle = angle_between(elbow, wrist, index)
            shoulder_angle  = get_shoulder_angle_yaxis(elbow,shoulder)
            shoulder_label, shoulder_score = reba_score_shoulder(abs(shoulder_angle))
            arm_label, arm_score = reba_score_arm(abs(arm_angle))
            wrist_label, wrist_score = reba_score_wrist(abs(wrist_angle))
            
            if side == "LEFT":
                frame_counts_left_arm[arm_label] += 1
                frame_counts_left_shoulder[shoulder_label] += 1
                frame_counts_left_wrist[wrist_label] += 1
                angles_over_time["left_arm"].append(arm_angle)
                angles_over_time["left_shoulder"].append(shoulder_angle)
                angles_over_time["left_wrist"].append(wrist_angle)
            else:
                frame_counts_right_arm[arm_label] += 1
                frame_counts_right_shoulder[shoulder_label] += 1
                frame_counts_right_wrist[wrist_label] += 1
                angles_over_time["right_arm"].append(arm_angle)
                angles_over_time["right_shoulder"].append(shoulder_angle)
                angles_over_time["right_wrist"].append(wrist_angle)
                
            arm_color = get_color_for_score(arm_score)
            shoulder_color = get_color_for_score(shoulder_score)
            wrist_color = get_color_for_score(wrist_score)
            
            cv2.line(frame, shoulder, elbow, shoulder_color, 2)
            cv2.line(frame, elbow, wrist, arm_color, 2)
            cv2.line(frame, wrist, index, wrist_color, 2)
            
            cv2.putText(frame,f"{side.title()} {arm_label}",(elbow[0] + 10, elbow[1] - 10),cv2.FONT_HERSHEY_SIMPLEX,0.6,arm_color,2)
            cv2.putText(frame,f"{side.title()} {shoulder_label}",(shoulder[0] + 10, shoulder[1] - 10),cv2.FONT_HERSHEY_SIMPLEX,0.6,shoulder_color,2)
            cv2.putText(frame,f"{side.title()} {wrist_label}",(wrist[0] + 10, wrist[1] - 10),cv2.FONT_HERSHEY_SIMPLEX,0.6,wrist_color,2)

        left_knee_angle = None
        right_knee_angle = None

        # Legs
        for side in ["LEFT", "RIGHT"]:
            hip = to(f"{side}_HIP")
            knee = to(f"{side}_KNEE")
            ankle = to(f"{side}_ANKLE")
            knee_angle = angle_between(hip, knee, ankle)
            leg_label, leg_score = reba_score_leg(knee_angle)
            frame_counts[leg_label] += 1
            if side == "LEFT":
                frame_counts_left_leg[leg_label] += 1
                angles_over_time["left_leg"].append(knee_angle)
                left_knee_angle = knee_angle
            else:
                frame_counts_right_leg[leg_label] += 1
                angles_over_time["right_leg"].append(knee_angle)
                right_knee_angle = knee_angle
            leg_color = get_color_for_score(leg_score)
            cv2.line(frame, hip, knee, leg_color, 2)
            cv2.line(frame, knee, ankle, leg_color, 2)
            cv2.putText(
            frame,
            f"{side.title()} {leg_label}",
            (knee[0] + 10, knee[1] - 10),
            cv2.FONT_HERSHEY_SIMPLEX,
            0.6,
            leg_color,
            2
            )
        leg_angle_diff = abs(left_knee_angle-right_knee_angle)
        leg_diff_label, leg_diff_score = reba_score_legdiff(leg_angle_diff)
        frame_counts[leg_diff_label] += 1
        angles_over_time["leg_diff"].append(leg_angle_diff)

        # Draw remaining skeleton
        for connection in pose_connections:
            start_idx, end_idx = connection
            pt1 = to_xy(lm[start_idx])
            pt2 = to_xy(lm[end_idx])
            if (start_idx, end_idx) in [
                (landmarks_enum.LEFT_SHOULDER, landmarks_enum.LEFT_ELBOW),
                (landmarks_enum.LEFT_ELBOW, landmarks_enum.LEFT_WRIST),
                (landmarks_enum.RIGHT_SHOULDER, landmarks_enum.RIGHT_ELBOW),
                (landmarks_enum.RIGHT_ELBOW, landmarks_enum.RIGHT_WRIST),
                (landmarks_enum.LEFT_HIP, landmarks_enum.LEFT_KNEE),
                (landmarks_enum.LEFT_KNEE, landmarks_enum.LEFT_ANKLE),
                (landmarks_enum.RIGHT_HIP, landmarks_enum.RIGHT_KNEE),
                (landmarks_enum.RIGHT_KNEE, landmarks_enum.RIGHT_ANKLE),
                (landmarks_enum.LEFT_WRIST, landmarks_enum.LEFT_INDEX),
                (landmarks_enum.RIGHT_WRIST, landmarks_enum.RIGHT_INDEX)
            ]:
                continue
            cv2.line(frame, pt1, pt2, (0, 255, 0), 2)

        # Text overlays
        cv2.putText(frame, neck_label,mid_ear_vis, cv2.FONT_HERSHEY_SIMPLEX, 0.6, neck_color, 2)
        cv2.putText(frame, trunk_label, mid_hip_vis, cv2.FONT_HERSHEY_SIMPLEX, 0.6, trunk_color, 2)

    out.write(frame)

# -------------------- Final Summary --------------------
cap.release()
out.release()
pose.close()

# Color mapping based on REBA score
score_colors = {
    0: "green",
    1: "yellow",
    2: "orange",
    3: "red",
    4: "black"
}

# Mapping from body part to REBA scoring function
reba_functions = {
    "neck": reba_score_neck,
    "left_arm": reba_score_arm,
    "right_arm": reba_score_arm,
    "left_leg": reba_score_leg,
    "right_leg": reba_score_leg,
    "trunk": reba_score_trunk,
    "left_shoulder": reba_score_shoulder,
    "right_shoulder": reba_score_shoulder,
    "left_wrist": reba_score_wrist,
    "right_wrist": reba_score_wrist,
    "leg_diff": reba_score_legdiff,
    "neck_twist": reba_score_neck_twist,
    "trunk_twist": reba_score_trunk_twist
}

# Save CSV
for key in angles_over_time:
    if key == "time":
        continue
    csv_path = os.path.join(output_dir, f"{key}_angle.csv")
    with open(csv_path, 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        writer.writerow(["Time (s)", f"{key.capitalize()} Angle (deg)"])
        for t, a in zip(angles_over_time["time"], angles_over_time[key]):
            writer.writerow([t, a])
            
            
for key in angles_over_time:
    if key == "time":
        continue

    if key not in reba_functions:
        print(f"Skipping {key} — no REBA function defined.")
        continue

    score_func = reba_functions[key]
    time_series = np.array(angles_over_time["time"])
    angle_series = np.array(angles_over_time[key])

    # Create line segments between each point
    points = np.array([time_series, angle_series]).T.reshape(-1, 1, 2)
    segments = np.concatenate([points[:-1], points[1:]], axis=1)

    # Compute REBA score for each segment using average angle
    avg_angles = (angle_series[:-1] + angle_series[1:]) / 2
    colors = [score_colors[score_func(abs(angle))[1]] for angle in avg_angles]

    # Create LineCollection with segment colors
    lc = LineCollection(segments, colors=colors, linewidths=2)

    plt.figure()
    ax = plt.gca()
    ax.add_collection(lc)
    ax.set_xlim(time_series.min(), time_series.max())
    ax.set_ylim(angle_series.min() - 5, angle_series.max() + 5)
    plt.xlabel("Time (s)")
    plt.ylabel("Angle (degrees)")
    plt.title(f"{key.capitalize()} Angle over Time")
    plt.grid(True)
    
    # Create custom legend handles
    legend_handles = [
        Line2D([0], [0], color=color, lw=3, label=f"REBA {score} point{'s' if score != 1 else ''}")
        for score, color in score_colors.items()
    ]
    plt.legend(handles=legend_handles, title="REBA Score")

    plt.tight_layout()
    plt.savefig(os.path.join(output_dir, f"{key}_angle_plot_colored.png"))
    plt.close()
    
print("\n⏱️ REBA Posture Duration Summary (per region):")
total_frames = sum(frame_counts.values())

for category, count in sorted(frame_counts_left_leg.items()):
    seconds = count / fps
    print(f"Left {category:<14}: {seconds:.2f} seconds")

for category, count in sorted(frame_counts_right_leg.items()):
    seconds = count / fps
    print(f"Right {category:<13}: {seconds:.2f} seconds")
    
for category, count in sorted(frame_counts_left_shoulder.items()):
    seconds = count / fps
    print(f"Left {category:<14}: {seconds:.2f} seconds")

for category, count in sorted(frame_counts_right_shoulder.items()):
    seconds = count / fps
    print(f"Right {category:<13}: {seconds:.2f} seconds")

for category, count in sorted(frame_counts_left_arm.items()):
    seconds = count / fps
    print(f"Left {category:<14}: {seconds:.2f} seconds")

for category, count in sorted(frame_counts_right_arm.items()):
    seconds = count / fps
    print(f"Right {category:<13}: {seconds:.2f} seconds")
    
for category, count in sorted(frame_counts_left_wrist.items()):
    seconds = count / fps
    print(f"Left {category:<14}: {seconds:.2f} seconds")

for category, count in sorted(frame_counts_right_wrist.items()):
    seconds = count / fps
    print(f"Right {category:<13}: {seconds:.2f} seconds")
    
for category, count in sorted(frame_counts.items()):
    if category not in frame_counts_left_leg and category not in frame_counts_right_leg and category not in frame_counts_left_arm and category not in frame_counts_right_arm and category not in frame_counts_right_shoulder and category not in frame_counts_left_shoulder and category not in frame_counts_right_wrist and category not in frame_counts_left_wrist:
        seconds = count / fps
        print(f"{category:<25}: {seconds:.2f} seconds")
        
for category, count in sorted(frame_counts_neck_twist.items()):
    seconds = count / fps
    print(f"{category:<13}: {seconds:.2f} seconds")

for category, count in sorted(frame_counts_trunk_twist.items()):
    seconds = count / fps
    print(f"{category:<13}: {seconds:.2f} seconds")

