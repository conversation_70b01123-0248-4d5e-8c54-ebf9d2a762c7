#!/usr/bin/env python3
"""
Check what jobs exist in the database
"""
import sys
import asyncio
import logging

# Add the app directory to Python path
sys.path.append('/app')

from app.db.database import AsyncSessionLocal
from app.crud import job as job_crud

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def check_jobs():
    """Check what jobs exist in the database"""
    
    try:
        async with AsyncSessionLocal() as session:
            # Get all jobs
            jobs = await job_crud.get_jobs(session, skip=0, limit=100)
            
            logger.info(f"Found {len(jobs)} jobs in database:")
            
            for job in jobs:
                logger.info(f"Job ID: {job.id}, Status: {job.status}, File: {job.file_path}")
                
            return jobs
            
    except Exception as e:
        logger.error(f"Error checking database: {e}")
        return []

if __name__ == "__main__":
    jobs = asyncio.run(check_jobs())
