#!/bin/bash

# REBA Video Analysis API - EC2 Deployment Script
# This script helps deploy the application to EC2

set -e  # Exit on any error

echo "🚀 REBA Video Analysis API - EC2 Deployment"
echo "============================================"

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    echo "❌ Please don't run this script as root"
    exit 1
fi

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo "📋 Checking prerequisites..."

if ! command_exists docker; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command_exists docker-compose; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

echo "✅ Prerequisites check passed"

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found. Creating from template..."
    if [ -f ".env.production" ]; then
        cp .env.production .env
        echo "📝 Please edit .env file with your production settings before continuing."
        echo "   Important: Update SECRET_KEY, database passwords, and domain settings."
        read -p "Press Enter after updating .env file..."
    else
        echo "❌ No .env template found. Please create .env file manually."
        exit 1
    fi
fi

# Ask for deployment type
echo ""
echo "🔧 Select deployment type:"
echo "1) Development (docker-compose.yml)"
echo "2) Production (docker-compose.prod.yml)"
read -p "Enter choice (1 or 2): " deploy_type

case $deploy_type in
    1)
        COMPOSE_FILE="docker-compose.yml"
        echo "📦 Using development configuration"
        ;;
    2)
        COMPOSE_FILE="docker-compose.prod.yml"
        echo "🏭 Using production configuration"
        ;;
    *)
        echo "❌ Invalid choice"
        exit 1
        ;;
esac

# Check if compose file exists
if [ ! -f "$COMPOSE_FILE" ]; then
    echo "❌ $COMPOSE_FILE not found"
    exit 1
fi

# Stop existing containers
echo ""
echo "🛑 Stopping existing containers..."
docker-compose -f $COMPOSE_FILE down || true

# Pull latest images
echo ""
echo "📥 Pulling latest images..."
docker-compose -f $COMPOSE_FILE pull

# Build application images
echo ""
echo "🔨 Building application images..."
docker-compose -f $COMPOSE_FILE build

# Start services
echo ""
echo "🚀 Starting services..."
docker-compose -f $COMPOSE_FILE up -d

# Wait for services to be ready
echo ""
echo "⏳ Waiting for services to be ready..."
sleep 10

# Check health
echo ""
echo "🏥 Checking service health..."

# Wait for web service to be ready
max_attempts=30
attempt=1
while [ $attempt -le $max_attempts ]; do
    if curl -f http://localhost:9000/api/v1/health >/dev/null 2>&1; then
        echo "✅ Web service is healthy"
        break
    else
        echo "⏳ Attempt $attempt/$max_attempts - waiting for web service..."
        sleep 5
        attempt=$((attempt + 1))
    fi
done

if [ $attempt -gt $max_attempts ]; then
    echo "❌ Web service failed to start properly"
    echo "📋 Checking logs..."
    docker-compose -f $COMPOSE_FILE logs web
    exit 1
fi

# Run database migrations
echo ""
echo "🗄️  Running database migrations..."
docker-compose -f $COMPOSE_FILE exec web alembic upgrade head

# Show status
echo ""
echo "📊 Service status:"
docker-compose -f $COMPOSE_FILE ps

echo ""
echo "🎉 Deployment completed successfully!"
echo ""
echo "📍 Service URLs:"
echo "   API Documentation: http://localhost:9000/api/v1/docs"
echo "   Health Check: http://localhost:9000/api/v1/health"
echo "   API Base: http://localhost:9000/api/v1"
echo ""
echo "🔧 Useful commands:"
echo "   View logs: docker-compose -f $COMPOSE_FILE logs -f"
echo "   Stop services: docker-compose -f $COMPOSE_FILE down"
echo "   Restart services: docker-compose -f $COMPOSE_FILE restart"
echo ""
echo "📝 Next steps:"
echo "   1. Test the API endpoints"
echo "   2. Upload a test video"
echo "   3. Configure domain and SSL (for production)"
echo "   4. Set up monitoring and backups"
