#!/bin/bash

# REBA Video Analysis API - Shared Nginx Deployment Script
# Use this when you already have Nginx running on the host

set -e  # Exit on any error

echo "🚀 REBA Video Analysis API - Shared Nginx Deployment"
echo "===================================================="

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    echo "❌ Please don't run this script as root"
    exit 1
fi

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check prerequisites
echo "📋 Checking prerequisites..."

if ! command_exists docker; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

if ! command_exists docker-compose; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

echo "✅ Prerequisites check passed"

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found. Creating from shared nginx template..."
    if [ -f ".env.shared-nginx" ]; then
        cp .env.shared-nginx .env
        echo "📝 Please edit .env file with your production settings before continuing."
        echo "   Important: Update SECRET_KEY, POSTGRES_PASSWORD, and domain settings."
        read -p "Press Enter after updating .env file..."
    else
        echo "❌ No .env template found. Please create .env file manually."
        exit 1
    fi
fi

# Use shared nginx compose file
COMPOSE_FILE="docker-compose.shared-nginx.yml"
echo "🔧 Using shared Nginx configuration: $COMPOSE_FILE"

# Check if compose file exists
if [ ! -f "$COMPOSE_FILE" ]; then
    echo "❌ $COMPOSE_FILE not found"
    exit 1
fi

# Check for port conflicts
echo ""
echo "🔍 Checking for port conflicts..."

if netstat -tuln | grep -q ":5433 "; then
    echo "⚠️  Port 5433 is already in use. Please stop the service using this port."
    echo "   Or modify the postgres port in $COMPOSE_FILE"
    read -p "Continue anyway? (y/N): " continue_anyway
    if [[ ! $continue_anyway =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

if netstat -tuln | grep -q ":6380 "; then
    echo "⚠️  Port 6380 is already in use. Please stop the service using this port."
    echo "   Or modify the redis port in $COMPOSE_FILE"
    read -p "Continue anyway? (y/N): " continue_anyway
    if [[ ! $continue_anyway =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

if netstat -tuln | grep -q ":9001 "; then
    echo "⚠️  Port 9001 is already in use. Please stop the service using this port."
    echo "   Or modify the web service port in $COMPOSE_FILE"
    read -p "Continue anyway? (y/N): " continue_anyway
    if [[ ! $continue_anyway =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# Stop existing containers
echo ""
echo "🛑 Stopping existing REBA containers..."
docker-compose -f $COMPOSE_FILE down || true

# Pull latest images
echo ""
echo "📥 Pulling latest images..."
docker-compose -f $COMPOSE_FILE pull

# Build application images
echo ""
echo "🔨 Building application images..."
docker-compose -f $COMPOSE_FILE build

# Start services
echo ""
echo "🚀 Starting REBA services..."
docker-compose -f $COMPOSE_FILE up -d

# Wait for services to be ready
echo ""
echo "⏳ Waiting for services to be ready..."
sleep 15

# Check health
echo ""
echo "🏥 Checking service health..."

# Wait for web service to be ready
max_attempts=30
attempt=1
while [ $attempt -le $max_attempts ]; do
    if curl -f http://localhost:9001/api/v1/health >/dev/null 2>&1; then
        echo "✅ REBA web service is healthy"
        break
    else
        echo "⏳ Attempt $attempt/$max_attempts - waiting for REBA web service..."
        sleep 5
        attempt=$((attempt + 1))
    fi
done

if [ $attempt -gt $max_attempts ]; then
    echo "❌ REBA web service failed to start properly"
    echo "📋 Checking logs..."
    docker-compose -f $COMPOSE_FILE logs reba-web
    exit 1
fi

# Run database migrations
echo ""
echo "🗄️  Running database migrations..."
docker-compose -f $COMPOSE_FILE exec reba-web alembic upgrade head

# Show status
echo ""
echo "📊 Service status:"
docker-compose -f $COMPOSE_FILE ps

echo ""
echo "🎉 REBA API deployment completed successfully!"
echo ""
echo "📍 Service Information:"
echo "   REBA API Backend: http://localhost:9001"
echo "   Health Check: http://localhost:9001/api/v1/health"
echo "   API Documentation: http://localhost:9001/api/v1/docs"
echo ""
echo "🔧 Next Steps:"
echo "   1. Update your Nginx configuration with the provided snippet"
echo "   2. Test Nginx configuration: sudo nginx -t"
echo "   3. Reload Nginx: sudo systemctl reload nginx"
echo "   4. Test the API through your Nginx proxy"
echo ""
echo "📝 Nginx Configuration:"
echo "   - Add the upstream block from nginx-reba-config.conf to your nginx.conf"
echo "   - Add the location blocks to your existing server block"
echo "   - Update the uploads path in the static files location"
echo ""
echo "🔧 Useful commands:"
echo "   View logs: docker-compose -f $COMPOSE_FILE logs -f"
echo "   Stop services: docker-compose -f $COMPOSE_FILE down"
echo "   Restart services: docker-compose -f $COMPOSE_FILE restart"
