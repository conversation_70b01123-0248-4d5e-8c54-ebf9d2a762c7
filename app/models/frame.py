from sqlalchemy import Column, String, Float, Integer, TIMESTAMP, ForeignKey, func, Boolean, BigInteger
from sqlalchemy.dialects.postgresql import UUID, JSONB
import uuid
from app.db.database import Base


class FrameMeasurement(Base):
    __tablename__ = "frame_measurements"
    
    id = Column(BigInteger, primary_key=True)
    job_id = Column(UUID(as_uuid=True), ForeignKey("jobs.id", ondelete="CASCADE"))
    frame_number = Column(Integer, nullable=False)
    timestamp_seconds = Column(Float, nullable=False)
    reba_components = Column(JSONB, nullable=False)
    final_reba_score = Column(Integer)
    pose_detected = Column(Boolean, default=True)
    created_at = Column(TIMESTAMP, server_default=func.now())
    
    def __repr__(self):
        return f"<FrameMeasurement(job_id={self.job_id}, frame={self.frame_number})>"
