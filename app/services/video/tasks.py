from celery import shared_task
from app.core.celery_app import celery_app
import uuid
import logging
import time
import os
from datetime import datetime

from app.db.database import AsyncSessionLocal
from app.crud import job as job_crud
from app.services.analysis.reba_processor import process_video_for_reba

logger = logging.getLogger(__name__)


@shared_task(bind=True, name="app.services.video.tasks.process_video")
def process_video(self, job_id: str):
    """
    Process a video for REBA analysis.
    This is a Celery task that runs in the background.
    """
    logger.info(f"Starting video processing for job {job_id}")

    try:
        # For testing: Skip database operations and just log progress
        logger.info(f"[DEBUG] Updating job {job_id} status to 'processing'")

        # Get job details from database
        logger.info(f"[DEBUG] Getting job details for {job_id}")

        # Import here to avoid circular imports
        from app.services.analysis.db_utils import get_job_details_sync

        # Get job from database using synchronous method
        job = get_job_details_sync(uuid.UUID(job_id))
        if not job:
            raise ValueError(f"Job {job_id} not found in database")

        logger.info(f"[DEBUG] Got job details: file_path={job.file_path}, options={job.processing_options}")

        # Process the video
        start_time = time.time()

        # Call the REBA processor with actual video analysis
        logger.info(f"[DEBUG] Starting REBA processing for job {job_id}")
        processing_options = job.processing_options or {}
        # Remove any forced mock data - let it analyze the actual video
        processing_options.pop('use_mock_data', None)

        result = process_video_for_reba(
            job.file_path,
            job.id,
            processing_options
        )

        processing_time = time.time() - start_time

        # Update job with results - for testing, just log it
        logger.info(f"[DEBUG] Updating job {job_id} status to 'completed'")
        logger.info(f"[DEBUG] Processing result: {result}")

        logger.info(f"Completed video processing for job {job_id} in {processing_time:.2f} seconds")

    except Exception as e:
        logger.exception(f"Error processing video for job {job_id}: {str(e)}")

        # Update job status to failed - for testing, just log it
        logger.info(f"[DEBUG] Updating job {job_id} status to 'failed': {str(e)}")

    return None  # Explicitly return None to avoid serialization issues


@shared_task(name="app.services.video.tasks.cleanup_old_jobs")
def cleanup_old_jobs(days: int = 30):
    """
    Clean up old jobs and their associated files.
    """
    logger.info(f"Starting cleanup of jobs older than {days} days")

    try:
        # For testing: Skip database operations and just log progress
        logger.info(f"[DEBUG] Would delete jobs older than {days} days")
        deleted_count = 0  # Mock deleted count

        logger.info(f"[DEBUG] Deleted {deleted_count} old jobs")

    except Exception as e:
        logger.exception(f"Error cleaning up old jobs: {str(e)}")

    return None  # Explicitly return None to avoid serialization issues
