from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func
from typing import List, Optional, Dict, Any
import uuid

from app.models.reba import REBAResult, REBADetailedScore, PoseDurationEvent, AngleSummaryStat


async def create_reba_result(db: AsyncSession, result_data: Dict[str, Any]) -> REBAResult:
    """Create a new REBA result in the database."""
    result = REBAResult(**result_data)
    db.add(result)
    await db.commit()
    await db.refresh(result)
    return result


async def get_reba_result(db: AsyncSession, job_id: uuid.UUID) -> Optional[REBAResult]:
    """Get a REBA result by job ID."""
    result = await db.execute(select(REBAResult).where(REBAResult.job_id == job_id))
    return result.scalars().first()


async def create_reba_detailed_score(db: AsyncSession, score_data: Dict[str, Any]) -> REBADetailedScore:
    """Create a new REBA detailed score in the database."""
    score = REBADetailedScore(**score_data)
    db.add(score)
    await db.commit()
    await db.refresh(score)
    return score


async def get_reba_detailed_score(db: AsyncSession, job_id: uuid.UUID) -> Optional[REBADetailedScore]:
    """Get a REBA detailed score by job ID."""
    result = await db.execute(select(REBADetailedScore).where(REBADetailedScore.job_id == job_id))
    return result.scalars().first()


async def create_pose_duration_event(db: AsyncSession, event_data: Dict[str, Any]) -> PoseDurationEvent:
    """Create a new pose duration event in the database."""
    event = PoseDurationEvent(**event_data)
    db.add(event)
    await db.commit()
    await db.refresh(event)
    return event


async def create_pose_duration_events(db: AsyncSession, events_data: List[Dict[str, Any]]) -> List[PoseDurationEvent]:
    """Create multiple pose duration events in the database."""
    events = [PoseDurationEvent(**event_data) for event_data in events_data]
    db.add_all(events)
    await db.commit()
    
    # Refresh all events
    for event in events:
        await db.refresh(event)
    
    return events


async def get_pose_duration_events(
    db: AsyncSession, 
    job_id: uuid.UUID, 
    body_part: Optional[str] = None,
    min_score: Optional[int] = None
) -> List[PoseDurationEvent]:
    """Get pose duration events for a job with optional filtering."""
    query = select(PoseDurationEvent).where(PoseDurationEvent.job_id == job_id)
    
    if body_part:
        query = query.where(PoseDurationEvent.body_part == body_part)
    
    if min_score is not None:
        query = query.where(PoseDurationEvent.final_score >= min_score)
    
    query = query.order_by(PoseDurationEvent.start_time_seconds)
    
    result = await db.execute(query)
    return result.scalars().all()


async def create_angle_summary_stat(db: AsyncSession, stat_data: Dict[str, Any]) -> AngleSummaryStat:
    """Create a new angle summary stat in the database."""
    stat = AngleSummaryStat(**stat_data)
    db.add(stat)
    await db.commit()
    await db.refresh(stat)
    return stat


async def create_angle_summary_stats(db: AsyncSession, stats_data: List[Dict[str, Any]]) -> List[AngleSummaryStat]:
    """Create multiple angle summary stats in the database."""
    stats = [AngleSummaryStat(**stat_data) for stat_data in stats_data]
    db.add_all(stats)
    await db.commit()
    
    # Refresh all stats
    for stat in stats:
        await db.refresh(stat)
    
    return stats


async def get_angle_summary_stats(
    db: AsyncSession, 
    job_id: uuid.UUID, 
    body_part: Optional[str] = None
) -> List[AngleSummaryStat]:
    """Get angle summary stats for a job with optional filtering."""
    query = select(AngleSummaryStat).where(AngleSummaryStat.job_id == job_id)
    
    if body_part:
        query = query.where(AngleSummaryStat.body_part == body_part)
    
    result = await db.execute(query)
    return result.scalars().all()


async def get_high_risk_events(
    db: AsyncSession, 
    job_id: uuid.UUID, 
    min_score: int = 8
) -> List[PoseDurationEvent]:
    """Get high risk pose duration events for a job."""
    result = await db.execute(
        select(PoseDurationEvent)
        .where(PoseDurationEvent.job_id == job_id)
        .where(PoseDurationEvent.final_score >= min_score)
        .order_by(PoseDurationEvent.start_time_seconds)
    )
    return result.scalars().all()


async def get_risk_statistics(db: AsyncSession, job_id: uuid.UUID):
    """Get risk statistics for a job."""
    result = await db.execute(
        select(
            PoseDurationEvent.final_score,
            func.sum(PoseDurationEvent.duration_seconds).label('total_duration'),
            func.count(PoseDurationEvent.id).label('event_count')
        )
        .where(PoseDurationEvent.job_id == job_id)
        .group_by(PoseDurationEvent.final_score)
    )
    return result.all()
