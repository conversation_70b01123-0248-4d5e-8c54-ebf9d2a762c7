#!/usr/bin/env python3
"""
Simple test to check if MediaPipe works in the current environment
"""
import sys
import logging
import numpy as np

# Add the app directory to Python path
sys.path.append('/app')

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_mediapipe_basic():
    """Test basic MediaPipe functionality"""
    
    try:
        logger.info("Testing MediaPipe import...")
        import mediapipe as mp
        logger.info("✓ MediaPipe imported successfully")
        
        logger.info("Testing MediaPipe Pose initialization...")
        mp_pose = mp.solutions.pose
        
        # Try the simplest possible configuration
        pose = mp_pose.Pose(
            static_image_mode=True,  # Use static mode like your working project
            model_complexity=0,      # Lightest model
            min_detection_confidence=0.5
        )
        logger.info("✓ MediaPipe Pose initialized successfully")
        
        # Test with a simple dummy image
        logger.info("Testing pose detection on dummy image...")
        test_image = np.zeros((480, 640, 3), dtype=np.uint8)
        results = pose.process(test_image)
        logger.info("✓ MediaPipe pose processing completed")
        
        if results.pose_landmarks:
            logger.info("✓ Pose landmarks detected (unexpected but good!)")
        else:
            logger.info("✓ No pose landmarks detected (expected for black image)")
        
        # Clean up
        pose.close()
        logger.info("✓ MediaPipe resources cleaned up")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ MediaPipe test failed: {e}")
        return False

def test_opencv_basic():
    """Test basic OpenCV functionality"""
    
    try:
        logger.info("Testing OpenCV import...")
        import cv2
        logger.info("✓ OpenCV imported successfully")
        
        # Test basic OpenCV operations
        logger.info("Testing OpenCV operations...")
        test_image = np.zeros((480, 640, 3), dtype=np.uint8)
        gray = cv2.cvtColor(test_image, cv2.COLOR_BGR2GRAY)
        rgb = cv2.cvtColor(test_image, cv2.COLOR_BGR2RGB)
        logger.info("✓ OpenCV color conversions work")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ OpenCV test failed: {e}")
        return False

if __name__ == "__main__":
    logger.info("=== Testing MediaPipe and OpenCV in Docker Environment ===")
    
    opencv_ok = test_opencv_basic()
    mediapipe_ok = test_mediapipe_basic()
    
    logger.info("\n=== Test Results ===")
    logger.info(f"OpenCV: {'✓ PASS' if opencv_ok else '✗ FAIL'}")
    logger.info(f"MediaPipe: {'✓ PASS' if mediapipe_ok else '✗ FAIL'}")
    
    if opencv_ok and mediapipe_ok:
        logger.info("🎉 All tests passed! MediaPipe should work for real video processing.")
    else:
        logger.info("❌ Some tests failed. Need to debug further.")
