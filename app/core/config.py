from pydantic_settings import BaseSettings
from typing import Optional, Dict, Any, List
import os
from pathlib import Path

# Define constants for problematic fields to bypass Pydantic validation
# 500MB in bytes
DEFAULT_MAX_FILE_SIZE = 524288000
# 30 minutes in seconds
DEFAULT_MAX_VIDEO_DURATION = 1800


class Settings(BaseSettings):
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "REBA Video Analysis API"
    
    # Security
    SECRET_KEY: str
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60
    
    # Database
    DATABASE_URL: str
    ASYNC_DATABASE_URL: str
    
    # Redis
    REDIS_HOST: str
    REDIS_PORT: int
    
    # Celery
    CELERY_BROKER_URL: str
    CELERY_RESULT_BACKEND: str
    
    # File Storage
    UPLOAD_DIR: str = "./uploads"
    # These fields are handled separately to avoid validation errors
    # MAX_FILE_SIZE and MAX_VIDEO_DURATION are accessed via properties
    ALLOWED_VIDEO_FORMATS: List[str] = ["mp4", "avi", "mov", "webm"]
    
    # Processing Settings
    DETECTION_CONFIDENCE: float = 0.87
    MODEL_COMPLEXITY: int = 2
    FRAME_RATE_REDUCTION: int = 1
    
    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": True,
        "protected_namespaces": (),  # Disable protected namespaces to avoid model_complexity warning
        "extra": "ignore"  # Ignore extra fields from environment variables
    }
    
    @property
    def max_file_size(self) -> int:
        """Get the max file size, handling potential comment in env var"""
        try:
            env_value = os.getenv("MAX_FILE_SIZE")
            if env_value:
                # Extract only the numeric part before any comment
                numeric_part = env_value.split('#')[0].strip()
                return int(numeric_part)
            return DEFAULT_MAX_FILE_SIZE
        except (ValueError, TypeError):
            return DEFAULT_MAX_FILE_SIZE
    
    @property
    def max_video_duration(self) -> int:
        """Get the max video duration, handling potential comment in env var"""
        try:
            env_value = os.getenv("MAX_VIDEO_DURATION")
            if env_value:
                # Extract only the numeric part before any comment
                numeric_part = env_value.split('#')[0].strip()
                return int(numeric_part)
            return DEFAULT_MAX_VIDEO_DURATION
        except (ValueError, TypeError):
            return DEFAULT_MAX_VIDEO_DURATION


settings = Settings()

# Ensure upload directory exists
upload_dir = Path(settings.UPLOAD_DIR)
upload_dir.mkdir(parents=True, exist_ok=True)
