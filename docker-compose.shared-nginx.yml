# Docker Compose for deployment with existing Nginx
# Use this when you already have Nginx running on the host

version: '3.8'

services:
  reba-postgres:
    image: postgres:15
    container_name: reba-postgres
    environment:
      POSTGRES_DB: reba_db
      POSTGRES_USER: reba_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-reba_password}
    ports:
      - "5433:5432"  # Different port to avoid conflicts
    volumes:
      - reba_postgres_data:/var/lib/postgresql/data
      - ./init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U reba_user -d reba_db"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    networks:
      - reba_network

  reba-redis:
    image: redis:7-alpine
    container_name: reba-redis
    ports:
      - "6380:6379"  # Different port to avoid conflicts
    volumes:
      - reba_redis_data:/data
    healthcheck:
      test: ["<PERSON><PERSON>", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    restart: unless-stopped
    networks:
      - reba_network

  reba-web:
    build: 
      context: .
      dockerfile: Dockerfile.prod
    container_name: reba-web
    ports:
      - "9001:8000"  # Expose on port 9001 for Nginx upstream
    environment:
      - DATABASE_URL=postgresql+asyncpg://reba_user:${POSTGRES_PASSWORD:-reba_password}@reba-postgres:5432/reba_db?ssl=disable
      - ASYNC_DATABASE_URL=postgresql+asyncpg://reba_user:${POSTGRES_PASSWORD:-reba_password}@reba-postgres:5432/reba_db?ssl=disable
      - REDIS_URL=redis://reba-redis:6379/0
      - REDIS_HOST=reba-redis
      - REDIS_PORT=6379
      - CELERY_BROKER_URL=redis://reba-redis:6379/0
      - CELERY_RESULT_BACKEND=redis://reba-redis:6379/0
      - SECRET_KEY=${SECRET_KEY:-temporarysecretkeyreplaceinproduction}
      - DEBUG=False
      - MEDIAPIPE_DISABLE_GPU=1
      - GLOG_logtostderr=1
    depends_on:
      reba-postgres:
        condition: service_healthy
      reba-redis:
        condition: service_healthy
    volumes:
      - ./uploads:/app/uploads
    restart: unless-stopped
    networks:
      - reba_network

  reba-celery:
    build: 
      context: .
      dockerfile: Dockerfile.prod
    container_name: reba-celery
    command: celery -A app.core.celery_app worker --loglevel=info --concurrency=2
    environment:
      - DATABASE_URL=postgresql+asyncpg://reba_user:${POSTGRES_PASSWORD:-reba_password}@reba-postgres:5432/reba_db?ssl=disable
      - ASYNC_DATABASE_URL=postgresql+asyncpg://reba_user:${POSTGRES_PASSWORD:-reba_password}@reba-postgres:5432/reba_db?ssl=disable
      - REDIS_URL=redis://reba-redis:6379/0
      - REDIS_HOST=reba-redis
      - REDIS_PORT=6379
      - CELERY_BROKER_URL=redis://reba-redis:6379/0
      - CELERY_RESULT_BACKEND=redis://reba-redis:6379/0
      - SECRET_KEY=${SECRET_KEY:-temporarysecretkeyreplaceinproduction}
      - MEDIAPIPE_DISABLE_GPU=1
      - GLOG_logtostderr=1
    depends_on:
      reba-postgres:
        condition: service_healthy
      reba-redis:
        condition: service_healthy
    volumes:
      - ./uploads:/app/uploads
    restart: unless-stopped
    networks:
      - reba_network

volumes:
  reba_postgres_data:
  reba_redis_data:

networks:
  reba_network:
    driver: bridge
