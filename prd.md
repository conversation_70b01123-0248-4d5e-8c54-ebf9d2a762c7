# REBA Video Analysis API - Product Requirements Document

## 1. Product Overview

### 1.1 Vision
Build a scalable FastAPI backend service that processes video uploads to perform real-time ergonomic risk assessment using REBA (Rapid Entire Body Assessment) methodology, providing actionable insights for workplace safety optimization.

### 1.2 Core Value Proposition
- **Automated Ergonomic Assessment**: Transform manual REBA scoring into automated video analysis
- **Real-time Risk Detection**: Identify high-risk postures and movements as they occur
- **Scalable Processing**: Handle concurrent video uploads with queue-based processing
- **Comprehensive Reporting**: Generate detailed analytics with temporal data and risk trends

## 2. Technical Architecture

### 2.1 High-Level Architecture
```
Client → API Gateway → FastAPI Backend → Processing Queue → Analysis Engine → Database
                                      ↓
                               File Storage (S3/Local)
```

### 2.2 Core Components

#### **API Layer (FastAPI)**
- RESTful endpoints for video upload, status tracking, and results retrieval
- JWT-based authentication and authorization
- Request validation and rate limiting
- Async request handling for high concurrency

#### **Processing Engine**
- **Queue System**: Redis/Celery for background task management
- **Video Processor**: MediaPipe-based pose detection pipeline (frame-level)
- **REBA Calculator**: Body part angle analysis and scoring engine
- **Duration Consolidator**: Converts frame-level data to pose duration events
- **Report Generator**: Statistical analysis and visualization creation

#### **Data Layer**
- **Primary Database**: PostgreSQL with JSONB for structured + flexible data
- **Alternative**: MongoDB for document-centric approach with native JSON
- **File Storage**: S3-compatible storage for videos and generated assets
- **Cache Layer**: Redis for session data and frequently accessed results
- **Optional**: InfluxDB for high-frequency frame-level time-series data

#### **Infrastructure**
- **Containerization**: Docker for service packaging
- **Queue Workers**: Scalable Celery workers for video processing
- **Load Balancing**: Handle multiple concurrent requests
- **Monitoring**: Health checks and performance metrics

## 3. API Specification

### 3.1 Core Endpoints

#### **Video Upload & Job Management**
```
POST /api/v1/videos/upload
GET /api/v1/jobs/{job_id}/status
GET /api/v1/jobs/{job_id}/results
DELETE /api/v1/jobs/{job_id}
GET /api/v1/jobs (list user jobs)
```

#### **Analysis & Reporting**
```
GET /api/v1/analysis/{job_id}/summary
GET /api/v1/analysis/{job_id}/timeline
GET /api/v1/analysis/{job_id}/body-parts
GET /api/v1/analysis/{job_id}/export (CSV/PDF)
```

#### **System & Health**
```
GET /api/v1/health
GET /api/v1/system/stats
```

### 3.2 Detailed Endpoint Specifications

#### **POST /api/v1/videos/upload**
**Purpose**: Initiate video processing job
**Request**:
```json
{
  "file": "multipart/form-data",
  "analysis_type": "reba",
  "options": {
    "detection_confidence": 0.87,
    "model_complexity": 2,
    "frame_rate_reduction": 1,
    "output_format": ["json", "csv", "video"]
  },
  "metadata": {
    "worker_id": "optional",
    "task_description": "optional",
    "department": "optional"
  }
}
```

**Response**:
```json
{
  "job_id": "uuid4",
  "status": "queued",
  "created_at": "2025-05-28T10:30:00Z",
  "estimated_completion": "2025-05-28T10:35:00Z",
  "file_info": {
    "filename": "video.mp4",
    "size_bytes": 15728640,
    "duration_seconds": 120,
    "fps": 30
  }
}
```

#### **GET /api/v1/jobs/{job_id}/status**
**Purpose**: Check processing status
**Response**:
```json
{
  "job_id": "uuid4",
  "status": "processing|completed|failed|queued",
  "progress_percentage": 75,
  "current_stage": "pose_detection|angle_calculation|report_generation",
  "created_at": "2025-05-28T10:30:00Z",
  "started_at": "2025-05-28T10:31:00Z",
  "completed_at": null,
  "error_message": null,
  "processing_stats": {
    "frames_processed": 2250,
    "total_frames": 3000,
    "poses_detected": 2180
  }
}
```

#### **GET /api/v1/jobs/{job_id}/results**
**Purpose**: Retrieve complete analysis results
**Response**:
```json
{
  "job_id": "uuid4",
  "analysis_summary": {
    "overall_risk_level": "moderate|high|low",
    "total_duration_seconds": 120,
    "frames_analyzed": 3000,
    "poses_detected": 2850,
    "detection_rate": 95.0
  },
  "reba_analysis": {
    "final_reba_score": 8,
    "risk_level": "high",
    "risk_description": "High risk, investigate and implement change",
    "component_breakdown": {
      "group_a": {
        "neck": {
          "base_score": 2,
          "modifiers": {
            "twisted": 1,
            "side_bending": 0
          },
          "final_score": 3,
          "pose_breakdown": {
            "neutral_0_20_deg": {
              "duration_seconds": 45.2,
              "percentage": 37.67,
              "base_score": 1
            },
            "flexion_over_20_deg": {
              "duration_seconds": 52.8,
              "percentage": 44.0,
              "base_score": 2
            },
            "extension": {
              "duration_seconds": 22.0,
              "percentage": 18.33,
              "base_score": 2
            }
          }
        },
        "trunk": {
          "base_score": 3,
          "modifiers": {
            "twisted": 0,
            "side_bending": 1
          },
          "final_score": 4,
          "pose_breakdown": {
            "upright_0_deg": {
              "duration_seconds": 18.2,
              "percentage": 15.17,
              "base_score": 1
            },
            "flexion_0_20_deg": {
              "duration_seconds": 35.6,
              "percentage": 29.67,
              "base_score": 2
            },
            "flexion_20_60_deg": {
              "duration_seconds": 42.3,
              "percentage": 35.25,
              "base_score": 3
            },
            "flexion_over_60_deg": {
              "duration_seconds": 23.9,
              "percentage": 19.92,
              "base_score": 4
            }
          }
        },
        "legs": {
          "score": 1,
          "pose_breakdown": {
            "both_legs_normal": {
              "duration_seconds": 102.4,
              "percentage": 85.33,
              "score": 1
            },
            "one_leg_raised": {
              "duration_seconds": 17.6,
              "percentage": 14.67,
              "score": 2
            }
          }
        },
        "posture_score_a": 6
      },
      "group_b": {
        "upper_arm": {
          "base_score": 2,
          "modifiers": {
            "shoulder_raised": 1,
            "abducted": 0,
            "supported": 0
          },
          "final_score": 3,
          "pose_breakdown": {
            "drop_20_to_plus_20": {
              "duration_seconds": 28.4,
              "percentage": 23.67,
              "base_score": 1
            },
            "extension_20_or_flexion_20_45": {
              "duration_seconds": 42.1,
              "percentage": 35.08,
              "base_score": 2
            },
            "flexion_45_90": {
              "duration_seconds": 35.7,
              "percentage": 29.75,
              "base_score": 3
            },
            "flexion_over_90": {
              "duration_seconds": 13.8,
              "percentage": 11.5,
              "base_score": 4
            }
          }
        },
        "lower_arm": {
          "score": 1,
          "pose_breakdown": {
            "flexion_60_100": {
              "duration_seconds": 78.2,
              "percentage": 65.17,
              "score": 1
            },
            "flexion_under_60_over_100": {
              "duration_seconds": 41.8,
              "percentage": 34.83,
              "score": 2
            }
          }
        },
        "wrist": {
          "base_score": 1,
          "modifiers": {
            "bent_from_midline": 1
          },
          "final_score": 2,
          "pose_breakdown": {
            "neutral_0_15_deg": {
              "duration_seconds": 67.3,
              "percentage": 56.08,
              "base_score": 1
            },
            "flexion_extension_over_15": {
              "duration_seconds": 52.7,
              "percentage": 43.92,
              "base_score": 2
            }
          }
        },
        "posture_score_b": 4
      },
      "modifiers": {
        "force_load_score": 1,
        "coupling_score": 0,
        "activity_score": 1
      }
    }
  },
  "risk_events": [
    {
      "timestamp": 34.5,
      "duration": 2.8,
      "body_parts": ["neck", "trunk"],
      "max_score": 4,
      "description": "High risk posture detected"
    }
  ],
  "assets": {
    "annotated_video_url": "/api/v1/assets/{job_id}/video",
    "timeline_chart_url": "/api/v1/assets/{job_id}/timeline.png",
    "csv_export_url": "/api/v1/assets/{job_id}/data.csv"
  }
}
```

## 4. Database Schema

### 4.1 Database Technology Recommendation

#### **Primary Recommendation: PostgreSQL with JSONB**
**Why PostgreSQL:**
- **Native JSON support**: JSONB columns for flexible REBA component storage
- **ACID compliance**: Reliable transactions for job processing
- **Excellent FastAPI integration**: SQLAlchemy + asyncpg for async operations
- **Indexing**: GIN indexes on JSONB for fast JSON queries
- **Scalability**: Proven performance for high-volume applications
- **SQL + NoSQL**: Best of both worlds - structured tables + flexible JSON

#### **Alternative: MongoDB**
**When to consider:**
- Document-heavy workflows (prefer pure JSON storage)
- Rapid prototyping with schema flexibility
- Team expertise with MongoDB

**FastAPI Integration:**
```python
# PostgreSQL + SQLAlchemy (Recommended)
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.dialects.postgresql import JSONB

# MongoDB + Motor (Alternative)
from motor.motor_asyncio import AsyncIOMotorClient
```

### 4.2 Core Tables (PostgreSQL Schema)

#### **jobs**
```sql
CREATE TABLE jobs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    status VARCHAR(20) NOT NULL DEFAULT 'queued',
    analysis_type VARCHAR(10) NOT NULL DEFAULT 'reba',
    created_at TIMESTAMP DEFAULT NOW(),
    started_at TIMESTAMP,
    completed_at TIMESTAMP,
    file_path VARCHAR(500),
    original_filename VARCHAR(255),
    file_size_bytes BIGINT,
    duration_seconds FLOAT,
    fps FLOAT,
    error_message TEXT,
    processing_options JSONB,
    metadata JSONB
);
```

#### **reba_results**
```sql
CREATE TABLE reba_results (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    job_id UUID REFERENCES jobs(id) ON DELETE CASCADE,
    overall_risk_level VARCHAR(10),
    total_frames INTEGER,
    poses_detected INTEGER,
    detection_rate FLOAT,
    processing_time_seconds FLOAT,
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### **reba_detailed_scores**
```sql
CREATE TABLE reba_detailed_scores (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    job_id UUID REFERENCES jobs(id) ON DELETE CASCADE,
    -- Individual component scores
    neck_base_score INTEGER NOT NULL,
    neck_twist_modifier INTEGER DEFAULT 0,
    neck_side_bending_modifier INTEGER DEFAULT 0,
    neck_final_score INTEGER NOT NULL,
    
    trunk_base_score INTEGER NOT NULL,
    trunk_twist_modifier INTEGER DEFAULT 0,
    trunk_side_bending_modifier INTEGER DEFAULT 0,
    trunk_final_score INTEGER NOT NULL,
    
    legs_score INTEGER NOT NULL,
    posture_score_a INTEGER NOT NULL,
    
    upper_arm_base_score INTEGER NOT NULL,
    upper_arm_shoulder_modifier INTEGER DEFAULT 0,
    upper_arm_abduction_modifier INTEGER DEFAULT 0,
    upper_arm_support_modifier INTEGER DEFAULT 0,
    upper_arm_final_score INTEGER NOT NULL,
    
    lower_arm_score INTEGER NOT NULL,
    
    wrist_base_score INTEGER NOT NULL,
    wrist_deviation_modifier INTEGER DEFAULT 0,
    wrist_final_score INTEGER NOT NULL,
    
    posture_score_b INTEGER NOT NULL,
    
    -- Force and activity modifiers
    force_load_score INTEGER DEFAULT 0,
    coupling_score INTEGER DEFAULT 0,
    activity_score INTEGER DEFAULT 0,
    
    final_reba_score INTEGER NOT NULL,
    risk_level VARCHAR(20) NOT NULL,
    
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### **pose_duration_events**
```sql
CREATE TABLE pose_duration_events (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    job_id UUID REFERENCES jobs(id) ON DELETE CASCADE,
    body_part VARCHAR(30) NOT NULL,
    pose_category VARCHAR(50) NOT NULL,
    start_time_seconds FLOAT NOT NULL,
    end_time_seconds FLOAT NOT NULL,
    duration_seconds FLOAT NOT NULL,
    angle_range VARCHAR(30),
    base_reba_score INTEGER,
    modifiers JSONB DEFAULT '{}',
    final_score INTEGER,
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### **frame_measurements** (Optional - for debugging/detailed analysis)
```sql
CREATE TABLE frame_measurements (
    id BIGSERIAL PRIMARY KEY,
    job_id UUID REFERENCES jobs(id) ON DELETE CASCADE,
    frame_number INTEGER NOT NULL,
    timestamp_seconds FLOAT NOT NULL,
    reba_components JSONB NOT NULL,
    final_reba_score INTEGER,
    pose_detected BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### **angle_measurements**
```sql
-- Simplified for aggregated data storage
CREATE TABLE angle_summary_stats (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    job_id UUID REFERENCES jobs(id) ON DELETE CASCADE,
    body_part VARCHAR(30) NOT NULL,
    avg_angle FLOAT,
    min_angle FLOAT,
    max_angle FLOAT,
    std_deviation FLOAT,
    total_measurements INTEGER,
    created_at TIMESTAMP DEFAULT NOW()
);
```

### 4.3 Indexes and Performance
```sql
-- Core job queries
CREATE INDEX idx_jobs_status ON jobs(status);
CREATE INDEX idx_jobs_created_at ON jobs(created_at DESC);

-- REBA analysis queries
CREATE INDEX idx_reba_results_job_id ON reba_results(job_id);
CREATE INDEX idx_reba_detailed_job_id ON reba_detailed_scores(job_id);

-- Duration event queries (primary analysis)
CREATE INDEX idx_pose_duration_job_body ON pose_duration_events(job_id, body_part);
CREATE INDEX idx_pose_duration_job_time ON pose_duration_events(job_id, start_time_seconds);
CREATE INDEX idx_pose_duration_category ON pose_duration_events(job_id, pose_category);

-- JSONB indexes for flexible queries
CREATE INDEX idx_reba_detailed_components ON reba_detailed_scores USING GIN (component_breakdown);
CREATE INDEX idx_frame_measurements_components ON frame_measurements USING GIN (reba_components);

-- Summary stats
CREATE INDEX idx_angle_summary_job_body ON angle_summary_stats(job_id, body_part);
```

### 4.4 Database Choice Analysis

#### **Option 1: PostgreSQL + JSONB (RECOMMENDED)**

**Advantages:**
- **Best of both worlds**: Relational structure + NoSQL flexibility
- **JSONB performance**: Binary JSON with indexing support
- **ACID compliance**: Reliable transactions for job state management
- **Mature ecosystem**: Extensive tooling, backup solutions, monitoring
- **Async support**: Native asyncpg driver for FastAPI
- **Complex queries**: SQL joins + JSON operations in single queries
- **Data integrity**: Foreign keys, constraints, migrations with Alembic

**Use Cases:**
- Production applications requiring reliability
- Complex analytical queries across multiple tables
- Need for data integrity and consistency
- Long-term data retention and reporting

**FastAPI Integration:**
```python
# requirements.txt
fastapi==0.104.1
sqlalchemy[asyncio]==2.0.23
asyncpg==0.29.0
alembic==1.13.1

# database.py
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker

DATABASE_URL = "postgresql+asyncpg://user:pass@localhost:5432/reba_db"
engine = create_async_engine(DATABASE_URL, echo=True)
AsyncSessionLocal = sessionmaker(engine, class_=AsyncSession, expire_on_commit=False)

async def get_db():
    async with AsyncSessionLocal() as session:
        yield session
```

#### **Option 2: MongoDB (ALTERNATIVE)**

**Advantages:**
- **Document-native**: Natural JSON storage without conversion
- **Schema flexibility**: Easy to evolve data structures
- **Horizontal scaling**: Built-in sharding capabilities
- **Rapid development**: No migrations, flexible schema
- **Aggregation pipeline**: Powerful data processing capabilities

**Disadvantages:**
- **Eventual consistency**: Less strict data integrity
- **Learning curve**: Different query paradigm
- **Limited joins**: Requires application-level data combining
- **Tooling**: Less mature FastAPI ecosystem

**FastAPI Integration:**
```python
# requirements.txt
motor==3.3.2  # Async MongoDB driver
beanie==1.23.1  # ODM for FastAPI

# database.py
from motor.motor_asyncio import AsyncIOMotorClient
from beanie import init_beanie
import asyncio

async def init_database():
    client = AsyncIOMotorClient("mongodb://localhost:27017")
    await init_beanie(
        database=client.reba_db,
        document_models=[Job, REBAResult, PoseDurationEvent]
    )
```

#### **Option 3: SQLite + JSON (DEVELOPMENT/PROTOTYPE)**

**Advantages:**
- **Zero setup**: File-based, no server required
- **Fast development**: Immediate start, no configuration
- **JSON1 extension**: Native JSON support
- **Portability**: Single file database

**Disadvantages:**
- **Single writer**: No concurrent writes
- **Limited scalability**: Not suitable for production
- **Basic JSON support**: Limited compared to PostgreSQL JSONB

**Use Case**: Development, testing, proof-of-concepts only

### 4.5 Detailed FastAPI Database Integration

#### **PostgreSQL Implementation (Recommended)**

**Project Structure:**
```
app/
├── models/
│   ├── __init__.py
│   ├── job.py
│   ├── reba.py
│   └── events.py
├── schemas/
│   ├── __init__.py
│   ├── job.py
│   └── reba.py
├── database.py
├── crud.py
└── main.py
```

**Database Models:**
```python
# models/job.py
from sqlalchemy import Column, String, Float, Integer, TIMESTAMP, func
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.ext.declarative import declarative_base
import uuid

Base = declarative_base()

class Job(Base):
    __tablename__ = "jobs"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    status = Column(String(20), nullable=False, default="queued")
    analysis_type = Column(String(10), nullable=False, default="reba")
    created_at = Column(TIMESTAMP, server_default=func.now())
    started_at = Column(TIMESTAMP, nullable=True)
    completed_at = Column(TIMESTAMP, nullable=True)
    file_path = Column(String(500))
    original_filename = Column(String(255))
    file_size_bytes = Column(Integer)
    duration_seconds = Column(Float)
    fps = Column(Float)
    error_message = Column(String)
    processing_options = Column(JSONB)
    metadata = Column(JSONB)

# models/reba.py
class REBADetailedScores(Base):
    __tablename__ = "reba_detailed_scores"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    job_id = Column(UUID(as_uuid=True), ForeignKey("jobs.id", ondelete="CASCADE"))
    
    # Store all REBA components as structured JSON
    neck_analysis = Column(JSONB)  # {base_score, modifiers, final_score, pose_breakdown}
    trunk_analysis = Column(JSONB)
    legs_analysis = Column(JSONB)
    upper_arm_analysis = Column(JSONB)
    lower_arm_analysis = Column(JSONB)
    wrist_analysis = Column(JSONB)
    
    posture_score_a = Column(Integer, nullable=False)
    posture_score_b = Column(Integer, nullable=False)
    force_load_score = Column(Integer, default=0)
    coupling_score = Column(Integer, default=0)
    activity_score = Column(Integer, default=0)
    final_reba_score = Column(Integer, nullable=False)
    risk_level = Column(String(20), nullable=False)
    
    created_at = Column(TIMESTAMP, server_default=func.now())

# models/events.py
class PoseDurationEvent(Base):
    __tablename__ = "pose_duration_events"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    job_id = Column(UUID(as_uuid=True), ForeignKey("jobs.id", ondelete="CASCADE"))
    body_part = Column(String(30), nullable=False)
    pose_category = Column(String(50), nullable=False)
    start_time_seconds = Column(Float, nullable=False)
    end_time_seconds = Column(Float, nullable=False)
    duration_seconds = Column(Float, nullable=False)
    angle_range = Column(String(30))
    base_reba_score = Column(Integer)
    modifiers = Column(JSONB, default={})
    final_score = Column(Integer)
    created_at = Column(TIMESTAMP, server_default=func.now())
```

**Pydantic Schemas:**
```python
# schemas/job.py
from pydantic import BaseModel, UUID4
from typing import Optional, Dict, Any
from datetime import datetime

class JobCreate(BaseModel):
    analysis_type: str = "reba"
    processing_options: Optional[Dict[str, Any]] = None
    metadata: Optional[Dict[str, Any]] = None

class JobResponse(BaseModel):
    id: UUID4
    status: str
    analysis_type: str
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    file_path: Optional[str] = None
    original_filename: Optional[str] = None
    file_size_bytes: Optional[int] = None
    duration_seconds: Optional[float] = None
    fps: Optional[float] = None
    error_message: Optional[str] = None
    
    class Config:
        from_attributes = True

# schemas/reba.py
class REBAComponentAnalysis(BaseModel):
    base_score: int
    modifiers: Dict[str, int]
    final_score: int
    pose_breakdown: Dict[str, Any]

class REBAResultResponse(BaseModel):
    final_reba_score: int
    risk_level: str
    risk_description: str
    neck_analysis: REBAComponentAnalysis
    trunk_analysis: REBAComponentAnalysis
    # ... other components
```

**CRUD Operations:**
```python
# crud.py
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.orm import selectinload
from typing import List, Optional

async def create_job(db: AsyncSession, job_data: dict) -> Job:
    job = Job(**job_data)
    db.add(job)
    await db.commit()
    await db.refresh(job)
    return job

async def get_job(db: AsyncSession, job_id: UUID) -> Optional[Job]:
    result = await db.execute(
        select(Job).where(Job.id == job_id)
    )
    return result.scalars().first()

async def update_job_status(db: AsyncSession, job_id: UUID, status: str, **kwargs):
    await db.execute(
        update(Job)
        .where(Job.id == job_id)
        .values(status=status, **kwargs)
    )
    await db.commit()

async def get_reba_results(db: AsyncSession, job_id: UUID) -> Optional[REBADetailedScores]:
    result = await db.execute(
        select(REBADetailedScores).where(REBADetailedScores.job_id == job_id)
    )
    return result.scalars().first()

async def get_pose_events(db: AsyncSession, job_id: UUID, body_part: Optional[str] = None) -> List[PoseDurationEvent]:
    query = select(PoseDurationEvent).where(PoseDurationEvent.job_id == job_id)
    if body_part:
        query = query.where(PoseDurationEvent.body_part == body_part)
    query = query.order_by(PoseDurationEvent.start_time_seconds)
    
    result = await db.execute(query)
    return result.scalars().all()

# Complex JSON queries example
async def get_high_risk_duration(db: AsyncSession, job_id: UUID, min_score: int = 8) -> List[PoseDurationEvent]:
    result = await db.execute(
        select(PoseDurationEvent)
        .where(PoseDurationEvent.job_id == job_id)
        .where(PoseDurationEvent.final_score >= min_score)
        .order_by(PoseDurationEvent.start_time_seconds)
    )
    return result.scalars().all()
```

**API Endpoints with Database Integration:**
```python
# main.py
from fastapi import FastAPI, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

app = FastAPI(title="REBA Analysis API")

@app.post("/api/v1/jobs", response_model=JobResponse)
async def create_analysis_job(
    job_data: JobCreate,
    db: AsyncSession = Depends(get_db)
):
    job = await create_job(db, job_data.dict())
    # Queue background task here
    return job

@app.get("/api/v1/jobs/{job_id}", response_model=JobResponse)
async def get_job_status(
    job_id: UUID4,
    db: AsyncSession = Depends(get_db)
):
    job = await get_job(db, job_id)
    if not job:
        raise HTTPException(status_code=404, detail="Job not found")
    return job

@app.get("/api/v1/jobs/{job_id}/results")
async def get_job_results(
    job_id: UUID4,
    db: AsyncSession = Depends(get_db)
):
    # Get REBA analysis
    reba_results = await get_reba_results(db, job_id)
    if not reba_results:
        raise HTTPException(status_code=404, detail="Results not found")
    
    # Get pose duration events
    pose_events = await get_pose_events(db, job_id)
    
    return {
        "reba_analysis": reba_results,
        "pose_events": pose_events
    }
```

### 4.6 Database Performance Optimization

#### **Connection Pooling:**
```python
# database.py
engine = create_async_engine(
    DATABASE_URL,
    pool_size=20,          # Connection pool size
    max_overflow=0,        # Additional connections
    pool_pre_ping=True,    # Validate connections
    pool_recycle=3600,     # Recycle connections every hour
    echo=False             # Set to True for SQL debugging
)
```

#### **JSONB Indexing for Fast Queries:**
```sql
-- Index for querying specific REBA components
CREATE INDEX idx_neck_analysis_score ON reba_detailed_scores 
USING GIN ((neck_analysis->'final_score'));

-- Index for pose breakdown queries
CREATE INDEX idx_neck_pose_breakdown ON reba_detailed_scores 
USING GIN ((neck_analysis->'pose_breakdown'));

-- Composite index for complex queries
CREATE INDEX idx_pose_events_composite ON pose_duration_events 
(job_id, body_part, final_score, start_time_seconds);
```

#### **Query Optimization Examples:**
```python
# Efficient pagination
async def get_jobs_paginated(db: AsyncSession, skip: int = 0, limit: int = 20):
    result = await db.execute(
        select(Job)
        .order_by(Job.created_at.desc())
        .offset(skip)
        .limit(limit)
    )
    return result.scalars().all()

# Aggregation queries
async def get_risk_statistics(db: AsyncSession, job_id: UUID):
    result = await db.execute(
        select(
            PoseDurationEvent.final_score,
            func.sum(PoseDurationEvent.duration_seconds).label('total_duration'),
            func.count(PoseDurationEvent.id).label('event_count')
        )
        .where(PoseDurationEvent.job_id == job_id)
        .group_by(PoseDurationEvent.final_score)
    )
    return result.all()
```

## 5. Processing Pipeline

### 5.1 Hybrid Frame-to-Duration Processing Architecture

#### **Processing Strategy: Frame → Duration**
**Frame-Level Processing (Internal)**:
- Every frame (30fps) → MediaPipe pose detection → Joint angles → REBA component scores
- High precision capture of brief high-risk postures
- Continuous assessment for REBA methodology compliance
- Better pose detection accuracy with consistent frame analysis

**Duration-Based Storage & Presentation**:
- Consecutive frames with same REBA scores → Pose events with start/end times
- Ergonomically relevant sustained posture analysis
- Efficient storage (reduces 3600 records/minute to ~10-50 events/minute)
- User-friendly presentation ("45 seconds in high-risk trunk flexion")

#### **Implementation Flow**:
```python
# Frame-level processing (temporary buffer)
frame_buffer = []
for frame in video:
    pose = detect_pose(frame)
    angles = calculate_angles(pose)
    reba_components = score_reba_components(angles)
    
    frame_buffer.append({
        'timestamp': frame_time,
        'reba_components': reba_components,
        'final_score': calculate_final_reba(reba_components)
    })

# Duration-level consolidation (persistent storage)
pose_events = consolidate_consecutive_frames(frame_buffer)
for event in pose_events:
    store_pose_duration_event(event)  # start_time, end_time, duration, scores
```

#### **Consolidation Rules**:
- **Minimum duration**: 0.5 seconds for pose events (filter detection noise)
- **Transition tolerance**: 0.2 second gaps allowed within same pose
- **State machine**: Track pose transitions and sustained periods
- **Frame buffering**: Process 1-2 second windows before consolidating

### 5.2 REBA Calculation Workflow
1. **Upload Validation**: File format, size, duration checks
2. **Job Creation**: Database entry and queue task creation
3. **Frame-Level Video Analysis**: MediaPipe pose detection at full frame rate
4. **Angle Extraction**: Calculate joint angles for all body parts per frame
5. **Frame-Level REBA Scoring**: 
   - Apply REBA component scoring to each frame
   - Store in temporary buffer (not persistent database)
6. **Duration Consolidation**:
   - Group consecutive frames with identical REBA component scores
   - Create pose duration events with start/end times
   - Filter brief transitions and detection noise
7. **Final REBA Calculation**: 
   - Step 1-3: Neck, Trunk, Legs → Table A lookup → Posture Score A
   - Step 7-9: Upper Arm, Lower Arm, Wrist → Table B lookup → Posture Score B
   - Step 10-13: Combine A+B via Table C, add Force/Coupling/Activity → Final REBA Score
8. **Statistical Analysis**: Calculate duration percentages and risk distributions
9. **Asset Generation**: Create annotated video, charts, CSV exports
10. **Result Storage**: Save duration events and final analysis to database
11. **Notification**: Update job status and notify completion

### 5.3 Data Structure Strategy
```json
{
  "processing_metadata": {
    "total_frames_processed": 3600,
    "processing_fps": 30,
    "pose_detection_rate": 95.2,
    "consolidation_events": 47
  },
  "duration_analysis": {
    "neck_forward_20_plus": {
      "total_duration_seconds": 45.3,
      "percentage_of_total": 37.75,
      "continuous_event_count": 8,
      "longest_continuous_duration": 12.4,
      "average_event_duration": 5.66,
      "events": [
        {"start": 23.1, "end": 35.5, "duration": 12.4, "reba_score": 2},
        {"start": 67.2, "end": 71.8, "duration": 4.6, "reba_score": 2}
      ]
    }
  }
}
```

### 5.4 REBA Table Implementation
The system must implement the official REBA lookup tables:
- **Table A**: 3D matrix (Neck × Trunk × Legs) → Posture Score A (1-9)
- **Table B**: 3D matrix (Upper Arm × Lower Arm × Wrist) → Posture Score B (1-9)  
- **Table C**: 2D matrix (Score A × Score B) → Base REBA Score (1-12)
- **Final Score**: Base + Force/Load + Coupling + Activity → Final REBA (1-15+)

### 5.5 Queue Management
- **Priority Levels**: File size-based prioritization, processing complexity
- **Worker Scaling**: Auto-scale based on queue depth and processing time
- **Failure Handling**: Retry logic with exponential backoff
- **Resource Management**: Memory and CPU limits per worker
- **Frame Buffer Management**: Efficient memory usage during processing

## 6. Data Formats & Standards

### 6.1 REBA Pose Categories & Scoring (Official REBA Methodology)

#### **Neck Position (Step 1)**
- **Neutral (1 point)**: 0-20° forward flexion
- **Moderate Flexion (2 points)**: >20° forward flexion  
- **Extension (2 points)**: Any backward extension
- **Modifiers**: 
  - Neck twisted: +1
  - Neck side bending: +1

#### **Trunk Position (Step 2)**
- **Upright (1 point)**: 0° (erect)
- **Flexion 0-20° (2 points)**: 0-20° forward flexion
- **Flexion 20-60° (3 points)**: 20-60° forward flexion  
- **Flexion >60° (4 points)**: >60° forward flexion
- **Extension (4 points)**: Any backward extension >20°
- **Modifiers**: 
  - Trunk twisted: +1
  - Trunk side bending: +1

#### **Legs Position (Step 3)**
- **Both Legs Normal (1 point)**: Both feet supported, walking/sitting
- **One Leg Raised (2 points)**: One leg not weight bearing
- **Both Legs Raised (2 points)**: Narrow base, unstable

#### **Upper Arm Position (Step 7)**
- **20° Drop to 20° Flexion (1 point)**: -20° to +20°
- **Extension >20° or 20-45° Flexion (2 points)**: >20° extension or 20-45° flexion
- **45-90° Flexion (3 points)**: 45-90° flexion
- **>90° Flexion (4 points)**: >90° flexion
- **Modifiers**:
  - Shoulder raised: +1
  - Upper arm abducted: +1
  - Arm supported or person leaning: -1

#### **Lower Arm Position (Step 8)**
- **60-100° Flexion (1 point)**: 60-100° at elbow
- **<60° or >100° Flexion (2 points)**: <60° or >100° at elbow

#### **Wrist Position (Step 9)**
- **Neutral (1 point)**: 0-15° flexion/extension
- **>15° Flexion/Extension (2 points)**: >15° from neutral
- **Modifiers**:
  - Wrist bent from midline or twisted: +1

#### **REBA Table Lookups**
- **Table A**: Combines Neck + Trunk + Legs scores → Posture Score A
- **Table B**: Combines Upper Arm + Lower Arm + Wrist scores → Posture Score B  
- **Table C**: Combines Posture Score A + Posture Score B → Final REBA Score

#### **Force/Load Adjustments**
- **<11 lbs**: +0
- **11-22 lbs**: +1  
- **>22 lbs**: +2
- **Shock or rapid build up**: +1

#### **Coupling Score (Grip Quality)**
- **Good**: +0 (well fitting handle, mid-range power grip)
- **Fair**: +1 (hand hold acceptable but not ideal, coupling acceptable with other body part)
- **Poor**: +2 (hand hold not acceptable but possible, no handles, awkward grip)
- **Unacceptable**: +3

#### **Activity Score**
- **Static hold >1 minute**: +1
- **Repeated small range actions (>4x per minute)**: +1
- **Action causes rapid large range changes or unstable base**: +1

#### **Final REBA Score Risk Levels**
- **1**: Negligible risk
- **2-3**: Low risk, change may be needed
- **4-7**: Medium risk, further investigate, change soon
- **8-10**: High risk, investigate and implement change
- **11+**: Very high risk, implement change immediately

### 6.2 Export Formats

#### **CSV Export Structure**
```csv
# Duration Events Export (Primary)
start_time,end_time,duration,body_part,pose_category,base_score,modifiers,final_score,risk_level
12.5,15.8,3.3,neck,forward_20_plus,2,twisted:1,3,medium
15.8,18.2,2.4,trunk,flexion_20_60,3,side_bending:1,4,high
20.1,22.7,2.6,upper_arm,flexion_45_90,3,shoulder_raised:1,4,high

# Summary Statistics Export
body_part,pose_category,total_duration,percentage,event_count,avg_duration,max_duration,reba_score
neck,forward_20_plus,45.3,37.75,8,5.66,12.4,2
trunk,flexion_20_60,32.1,26.75,12,2.68,8.9,3
upper_arm,flexion_45_90,28.7,23.92,15,1.91,6.3,3

# Frame-Level Export (Optional, for detailed analysis)
timestamp,frame,neck_angle,neck_score,trunk_angle,trunk_score,final_reba
0.033,1,22.1,2,25.3,3,8
0.066,2,23.4,2,26.1,3,8
```

## 7. Error Handling & Edge Cases

### 7.1 Processing Failures
- **Pose Detection Failure**: Insufficient poses detected (<50%)
- **File Corruption**: Invalid video format or corrupted data
- **Timeout Handling**: Long processing jobs with progress tracking
- **Resource Exhaustion**: Queue overflow and worker scaling

### 7.2 Validation Rules
- **File Size**: Max 500MB per upload
- **Duration**: Max 30 minutes per video
- **Format**: Support MP4, AVI, MOV, WebM
- **Resolution**: Minimum 480p for reliable pose detection

## 8. Future Extensibility (Post-MVP)

### 8.1 Additional Assessment Methods
- **RULA Integration**: Upper limb assessment
- **NIOSH Lifting**: Lifting task analysis
- **Custom Scoring**: Industry-specific modifications

### 8.2 Advanced Features
- **Real-time Processing**: WebRTC for live video analysis
- **Multi-person Detection**: Workplace-wide assessment
- **Comparative Analytics**: Trend analysis across multiple sessions
- **AI Recommendations**: Machine learning-based improvement suggestions

## 9. Performance Requirements

### 9.1 Processing Targets
- **Upload Response**: < 2 seconds for job creation
- **Processing Speed**: 1-2x real-time (2-minute video in 2-4 minutes)
- **Concurrent Jobs**: Support 50+ simultaneous processing jobs
- **API Response Time**: < 500ms for status/results endpoints

### 9.2 Scalability
- **Horizontal Scaling**: Stateless workers for easy scaling
- **Database Performance**: Optimized queries for large datasets
- **Storage Management**: Automatic cleanup of old job data
- **Caching Strategy**: Redis for frequently accessed data

This PRD provides a comprehensive foundation for building a production-ready REBA analysis system that can scale and extend to additional ergonomic assessment methodologies.