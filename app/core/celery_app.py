from celery import Celery
from app.core.config import settings

celery_app = Celery(
    "reba_worker",
    broker=settings.CELERY_BROKER_URL,
    backend=settings.CELERY_RESULT_BACKEND,
    include=["app.services.video.tasks", "app.services.analysis.tasks"]
)

# Configure Celery
celery_app.conf.update(
    task_serializer="json",
    accept_content=["json"],
    result_serializer="json",
    timezone="UTC",
    enable_utc=True,
    worker_prefetch_multiplier=1,
    task_acks_late=True,
    task_reject_on_worker_lost=True,
    task_time_limit=3600,  # 1 hour timeout for tasks
    task_soft_time_limit=3300,  # 55 minutes soft timeout
)

# Optional: Define task routes for different queues
# Commented out to use default queue for now - all tasks go to 'celery' queue
# celery_app.conf.task_routes = {
#     "app.services.video.tasks.*": {"queue": "video_processing"},
#     "app.services.analysis.tasks.*": {"queue": "analysis"},
# }

# Optional: Define periodic tasks if needed
celery_app.conf.beat_schedule = {
    "cleanup-old-jobs": {
        "task": "app.services.video.tasks.cleanup_old_jobs",
        "schedule": 86400.0,  # Once per day
    },
}
