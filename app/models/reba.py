from sqlalchemy import Column, String, Float, Integer, TIMESTAMP, ForeignKey, func
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship
import uuid
from app.db.database import Base


class REBAResult(Base):
    __tablename__ = "reba_results"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    job_id = Column(UUID(as_uuid=True), ForeignKey("jobs.id", ondelete="CASCADE"))
    overall_risk_level = Column(String(10))
    total_frames = Column(Integer)
    poses_detected = Column(Integer)
    detection_rate = Column(Float)
    processing_time_seconds = Column(Float)
    created_at = Column(TIMESTAMP, server_default=func.now())
    
    def __repr__(self):
        return f"<REBAResult(job_id={self.job_id}, risk_level={self.overall_risk_level})>"


class REBADetailedScore(Base):
    __tablename__ = "reba_detailed_scores"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    job_id = Column(UUID(as_uuid=True), ForeignKey("jobs.id", ondelete="CASCADE"))
    
    # Group A components
    neck_base_score = Column(Integer, nullable=False)
    neck_twist_modifier = Column(Integer, default=0)
    neck_side_bending_modifier = Column(Integer, default=0)
    neck_final_score = Column(Integer, nullable=False)
    neck_analysis = Column(JSONB)  # Detailed breakdown of neck poses
    
    trunk_base_score = Column(Integer, nullable=False)
    trunk_twist_modifier = Column(Integer, default=0)
    trunk_side_bending_modifier = Column(Integer, default=0)
    trunk_final_score = Column(Integer, nullable=False)
    trunk_analysis = Column(JSONB)  # Detailed breakdown of trunk poses
    
    legs_score = Column(Integer, nullable=False)
    legs_analysis = Column(JSONB)  # Detailed breakdown of leg poses
    
    posture_score_a = Column(Integer, nullable=False)
    
    # Group B components
    upper_arm_base_score = Column(Integer, nullable=False)
    upper_arm_shoulder_modifier = Column(Integer, default=0)
    upper_arm_abduction_modifier = Column(Integer, default=0)
    upper_arm_support_modifier = Column(Integer, default=0)
    upper_arm_final_score = Column(Integer, nullable=False)
    upper_arm_analysis = Column(JSONB)  # Detailed breakdown of upper arm poses
    
    lower_arm_score = Column(Integer, nullable=False)
    lower_arm_analysis = Column(JSONB)  # Detailed breakdown of lower arm poses
    
    wrist_base_score = Column(Integer, nullable=False)
    wrist_deviation_modifier = Column(Integer, default=0)
    wrist_final_score = Column(Integer, nullable=False)
    wrist_analysis = Column(JSONB)  # Detailed breakdown of wrist poses
    
    posture_score_b = Column(Integer, nullable=False)
    
    # Force and activity modifiers
    force_load_score = Column(Integer, default=0)
    coupling_score = Column(Integer, default=0)
    activity_score = Column(Integer, default=0)
    
    # Final scores
    final_reba_score = Column(Integer, nullable=False)
    risk_level = Column(String(20), nullable=False)
    
    created_at = Column(TIMESTAMP, server_default=func.now())
    
    def __repr__(self):
        return f"<REBADetailedScore(job_id={self.job_id}, final_score={self.final_reba_score})>"


class PoseDurationEvent(Base):
    __tablename__ = "pose_duration_events"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    job_id = Column(UUID(as_uuid=True), ForeignKey("jobs.id", ondelete="CASCADE"))
    body_part = Column(String(30), nullable=False)
    pose_category = Column(String(50), nullable=False)
    start_time_seconds = Column(Float, nullable=False)
    end_time_seconds = Column(Float, nullable=False)
    duration_seconds = Column(Float, nullable=False)
    angle_range = Column(String(30))
    base_reba_score = Column(Integer)
    modifiers = Column(JSONB, default={})
    final_score = Column(Integer)
    created_at = Column(TIMESTAMP, server_default=func.now())
    
    def __repr__(self):
        return f"<PoseDurationEvent(job_id={self.job_id}, body_part={self.body_part}, duration={self.duration_seconds})>"


class AngleSummaryStat(Base):
    __tablename__ = "angle_summary_stats"
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    job_id = Column(UUID(as_uuid=True), ForeignKey("jobs.id", ondelete="CASCADE"))
    body_part = Column(String(30), nullable=False)
    avg_angle = Column(Float)
    min_angle = Column(Float)
    max_angle = Column(Float)
    std_deviation = Column(Float)
    total_measurements = Column(Integer)
    created_at = Column(TIMESTAMP, server_default=func.now())
    
    def __repr__(self):
        return f"<AngleSummaryStat(job_id={self.job_id}, body_part={self.body_part})>"
