# docker-compose.yml

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: reba_db
      POSTGRES_USER: reba_user
      POSTGRES_PASSWORD: reba_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U reba_user -d reba_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3

  # NEW: Add FastAPI container (won't affect existing services)
  web:
    build: .
    ports:
      - "9000:8000"
    environment:
      # Connect to existing postgres and redis containers
      - DATABASE_URL=postgresql+asyncpg://reba_user:reba_password@postgres:5432/reba_db?ssl=disable
      - ASYNC_DATABASE_URL=postgresql+asyncpg://reba_user:reba_password@postgres:5432/reba_db?ssl=disable
      - REDIS_URL=redis://redis:6379/0
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - SECRET_KEY=temporarysecretkeyreplaceinproduction
      - DEBUG=True
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./uploads:/app/uploads
      - .:/app  # Mount source code for development
    networks:
      - default

  # NEW: Add Celery worker container
  celery:
    build: .
    command: celery -A app.core.celery_app worker --loglevel=info
    environment:
      - DATABASE_URL=postgresql+asyncpg://reba_user:reba_password@postgres:5432/reba_db?ssl=disable
      - ASYNC_DATABASE_URL=postgresql+asyncpg://reba_user:reba_password@postgres:5432/reba_db?ssl=disable
      - REDIS_URL=redis://redis:6379/0
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/0
      - SECRET_KEY=temporarysecretkeyreplaceinproduction
      - MEDIAPIPE_DISABLE_GPU=1
      - GLOG_logtostderr=1
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - ./uploads:/app/uploads
      - .:/app  # Mount source code for development
    networks:
      - default

volumes:
  postgres_data:
  redis_data: